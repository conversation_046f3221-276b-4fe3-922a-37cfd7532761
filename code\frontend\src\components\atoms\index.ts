/**
 * Atoms Components - Export Index
 * 
 * Basic building blocks of the design system
 * These are the smallest components that cannot be broken down further
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Basic UI Elements
export { Button } from './Button';
export { Input } from './Input';
export { Text } from './Text';
export { Box } from './Box';
export { Badge } from './Badge';
export { LoadingSpinner } from './LoadingSpinner';

// Performance-Optimized Components
export { LazyImage } from './LazyImage';
export { LazyComponent } from './LazyComponent';
export { LazyFlatList } from './LazyFlatList';

// Interaction Components
export {
  MicroInteractions,
  useMicroInteraction,
  withMicroInteraction,
  MicroInteractionProvider,
} from './MicroInteractions';

// Export types
export type { ButtonProps } from './Button';
export type { InputProps } from './Input';
export type { TextProps } from './Text';
export type { BoxProps } from './Box';
export type { BadgeProps } from './Badge';
export type { LoadingSpinnerProps } from './LoadingSpinner';
