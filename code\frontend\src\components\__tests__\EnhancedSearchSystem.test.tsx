/**
 * Enhanced Search System Tests
 * Comprehensive test suite for advanced search functionality with voice support
 * Following TDD protocol - these tests will initially fail
 */

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import * as Speech from 'expo-speech';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  EnhancedSearchSystem,
  SearchFilters,
  SearchSuggestions,
  VoiceSearch,
  useSearchAnalytics,
  SearchProvider,
} from '../search/EnhancedSearchSystem';

// Mock dependencies
jest.mock('expo-speech', () => ({
  speak: jest.fn(),
  stop: jest.fn(),
  isSpeakingAsync: jest.fn().mockResolvedValue(false),
  getAvailableVoicesAsync: jest.fn().mockResolvedValue([]),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  getAllKeys: jest.fn(),
  multiGet: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
  clear: jest.fn(),
}));

jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Alert: {
      alert: jest.fn(),
    },
    Dimensions: {
      get: jest.fn().mockReturnValue({ width: 375, height: 812 }),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
  };
});

// Mock API service
const mockSearchApi = {
  search: jest.fn(),
  getSuggestions: jest.fn(),
  getRecommendations: jest.fn(),
  trackSearch: jest.fn(),
};

jest.mock('../../services/searchApiService', () => ({
  searchApiService: mockSearchApi,
}));

describe('Enhanced Search System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
    mockSearchApi.search.mockResolvedValue({
      results: [],
      total_count: 0,
      has_next: false,
      has_previous: false,
    });
    mockSearchApi.getSuggestions.mockResolvedValue([]);
    mockSearchApi.getRecommendations.mockResolvedValue([]);
  });

  describe('Core Search Functionality', () => {
    it('should render search input with placeholder', () => {
      const { getByPlaceholderText } = render(
        <SearchProvider>
          <EnhancedSearchSystem placeholder="Search services..." />
        </SearchProvider>
      );

      expect(getByPlaceholderText('Search services...')).toBeTruthy();
    });

    it('should handle text input changes', async () => {
      const onSearch = jest.fn();
      const { getByPlaceholderText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..." 
            onSearch={onSearch}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair salon');

      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('hair salon', expect.any(Object));
      });
    });

    it('should debounce search queries', async () => {
      const onSearch = jest.fn();
      const { getByPlaceholderText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..." 
            onSearch={onSearch}
            debounceMs={300}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      
      // Type multiple characters quickly
      fireEvent.changeText(searchInput, 'h');
      fireEvent.changeText(searchInput, 'ha');
      fireEvent.changeText(searchInput, 'hair');

      // Should not call onSearch immediately
      expect(onSearch).not.toHaveBeenCalled();

      // Wait for debounce
      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('hair', expect.any(Object));
      }, { timeout: 500 });

      // Should only be called once after debounce
      expect(onSearch).toHaveBeenCalledTimes(1);
    });

    it('should handle minimum query length', async () => {
      const onSearch = jest.fn();
      const { getByPlaceholderText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..." 
            onSearch={onSearch}
            minQueryLength={3}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      
      // Type less than minimum length
      fireEvent.changeText(searchInput, 'ha');
      
      await waitFor(() => {
        expect(onSearch).not.toHaveBeenCalled();
      });

      // Type minimum length
      fireEvent.changeText(searchInput, 'hair');
      
      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('hair', expect.any(Object));
      });
    });

    it('should clear search input', () => {
      const { getByPlaceholderText, getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem placeholder="Search services..." />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair salon');

      const clearButton = getByTestId('clear-search-button');
      fireEvent.press(clearButton);

      expect(searchInput.props.value).toBe('');
    });
  });

  describe('Voice Search Functionality', () => {
    it('should render voice search button when enabled', () => {
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem enableVoiceSearch={true} />
        </SearchProvider>
      );

      expect(getByTestId('voice-search-button')).toBeTruthy();
    });

    it('should not render voice search button when disabled', () => {
      const { queryByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem enableVoiceSearch={false} />
        </SearchProvider>
      );

      expect(queryByTestId('voice-search-button')).toBeNull();
    });

    it('should handle voice search activation', async () => {
      const onVoiceSearch = jest.fn();
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            enableVoiceSearch={true}
            onVoiceSearch={onVoiceSearch}
          />
        </SearchProvider>
      );

      const voiceButton = getByTestId('voice-search-button');
      fireEvent.press(voiceButton);

      await waitFor(() => {
        expect(onVoiceSearch).toHaveBeenCalled();
      });
    });

    it('should show voice search modal', async () => {
      const { getByTestId, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem enableVoiceSearch={true} />
        </SearchProvider>
      );

      const voiceButton = getByTestId('voice-search-button');
      fireEvent.press(voiceButton);

      await waitFor(() => {
        expect(getByText('Listening...')).toBeTruthy();
      });
    });

    it('should handle voice search results', async () => {
      const onSearch = jest.fn();
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            enableVoiceSearch={true}
            onSearch={onSearch}
          />
        </SearchProvider>
      );

      const voiceButton = getByTestId('voice-search-button');
      fireEvent.press(voiceButton);

      // Simulate voice recognition result
      await act(async () => {
        // This would be triggered by actual voice recognition
        fireEvent(voiceButton, 'voiceResult', { transcript: 'hair salon near me' });
      });

      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('hair salon near me', expect.any(Object));
      });
    });

    it('should handle voice search errors', async () => {
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem enableVoiceSearch={true} />
        </SearchProvider>
      );

      const voiceButton = getByTestId('voice-search-button');
      fireEvent.press(voiceButton);

      // Simulate voice recognition error
      await act(async () => {
        fireEvent(voiceButton, 'voiceError', { error: 'No speech detected' });
      });

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Voice Search Error',
          'No speech detected. Please try again.',
          expect.any(Array)
        );
      });
    });
  });

  describe('Search Suggestions', () => {
    it('should show suggestions when typing', async () => {
      mockSearchApi.getSuggestions.mockResolvedValue([
        { text: 'hair salon', type: 'service', count: 15 },
        { text: 'hair cut', type: 'service', count: 8 },
      ]);

      const { getByPlaceholderText, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            enableRealTimeSearch={true}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair');

      await waitFor(() => {
        expect(getByText('hair salon')).toBeTruthy();
        expect(getByText('hair cut')).toBeTruthy();
      });
    });

    it('should handle suggestion selection', async () => {
      const onSearch = jest.fn();
      mockSearchApi.getSuggestions.mockResolvedValue([
        { text: 'hair salon', type: 'service', count: 15 },
      ]);

      const { getByPlaceholderText, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            onSearch={onSearch}
            enableRealTimeSearch={true}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair');

      await waitFor(() => {
        expect(getByText('hair salon')).toBeTruthy();
      });

      fireEvent.press(getByText('hair salon'));

      expect(onSearch).toHaveBeenCalledWith('hair salon', expect.any(Object));
    });

    it('should limit number of suggestions', async () => {
      const suggestions = Array.from({ length: 20 }, (_, i) => ({
        text: `suggestion ${i}`,
        type: 'service' as const,
        count: i,
      }));
      mockSearchApi.getSuggestions.mockResolvedValue(suggestions);

      const { getByPlaceholderText, queryByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            maxSuggestions={5}
            enableRealTimeSearch={true}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'test');

      await waitFor(() => {
        expect(queryByText('suggestion 0')).toBeTruthy();
        expect(queryByText('suggestion 4')).toBeTruthy();
        expect(queryByText('suggestion 5')).toBeNull();
      });
    });

    it('should hide suggestions when input is cleared', async () => {
      mockSearchApi.getSuggestions.mockResolvedValue([
        { text: 'hair salon', type: 'service', count: 15 },
      ]);

      const { getByPlaceholderText, getByText, queryByText, getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            enableRealTimeSearch={true}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair');

      await waitFor(() => {
        expect(getByText('hair salon')).toBeTruthy();
      });

      const clearButton = getByTestId('clear-search-button');
      fireEvent.press(clearButton);

      await waitFor(() => {
        expect(queryByText('hair salon')).toBeNull();
      });
    });
  });

  describe('Search Filters', () => {
    it('should show filters button', () => {
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem />
        </SearchProvider>
      );

      expect(getByTestId('filters-button')).toBeTruthy();
    });

    it('should toggle filters panel', async () => {
      const { getByTestId, getByText, queryByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem />
        </SearchProvider>
      );

      const filtersButton = getByTestId('filters-button');
      
      // Initially hidden
      expect(queryByText('Category')).toBeNull();

      // Show filters
      fireEvent.press(filtersButton);
      await waitFor(() => {
        expect(getByText('Category')).toBeTruthy();
      });

      // Hide filters
      fireEvent.press(filtersButton);
      await waitFor(() => {
        expect(queryByText('Category')).toBeNull();
      });
    });

    it('should handle category filter selection', async () => {
      const onSearch = jest.fn();
      const { getByTestId, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem onSearch={onSearch} />
        </SearchProvider>
      );

      const filtersButton = getByTestId('filters-button');
      fireEvent.press(filtersButton);

      await waitFor(() => {
        expect(getByText('Category')).toBeTruthy();
      });

      const categoryOption = getByText('Hair & Beauty');
      fireEvent.press(categoryOption);

      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('', expect.objectContaining({
          categories: ['hair-beauty'],
        }));
      });
    });

    it('should handle price range filter', async () => {
      const onSearch = jest.fn();
      const { getByTestId, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem onSearch={onSearch} />
        </SearchProvider>
      );

      const filtersButton = getByTestId('filters-button');
      fireEvent.press(filtersButton);

      await waitFor(() => {
        expect(getByText('Price Range')).toBeTruthy();
      });

      const priceSlider = getByTestId('price-range-slider');
      fireEvent(priceSlider, 'valueChange', [2, 4]);

      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('', expect.objectContaining({
          priceRange: [2, 4],
        }));
      });
    });

    it('should handle rating filter', async () => {
      const onSearch = jest.fn();
      const { getByTestId, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem onSearch={onSearch} />
        </SearchProvider>
      );

      const filtersButton = getByTestId('filters-button');
      fireEvent.press(filtersButton);

      await waitFor(() => {
        expect(getByText('Minimum Rating')).toBeTruthy();
      });

      const ratingButton = getByTestId('rating-4-stars');
      fireEvent.press(ratingButton);

      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('', expect.objectContaining({
          rating: 4,
        }));
      });
    });

    it('should handle distance filter', async () => {
      const onSearch = jest.fn();
      const { getByTestId, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem onSearch={onSearch} />
        </SearchProvider>
      );

      const filtersButton = getByTestId('filters-button');
      fireEvent.press(filtersButton);

      await waitFor(() => {
        expect(getByText('Distance')).toBeTruthy();
      });

      const distanceSlider = getByTestId('distance-slider');
      fireEvent(distanceSlider, 'valueChange', 10);

      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('', expect.objectContaining({
          distance: 10,
        }));
      });
    });

    it('should clear all filters', async () => {
      const onSearch = jest.fn();
      const { getByTestId, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem onSearch={onSearch} />
        </SearchProvider>
      );

      const filtersButton = getByTestId('filters-button');
      fireEvent.press(filtersButton);

      await waitFor(() => {
        expect(getByText('Clear All')).toBeTruthy();
      });

      const clearButton = getByText('Clear All');
      fireEvent.press(clearButton);

      await waitFor(() => {
        expect(onSearch).toHaveBeenCalledWith('', expect.objectContaining({
          categories: [],
          priceRange: [1, 5],
          rating: 0,
          distance: 25,
        }));
      });
    });
  });

  describe('Search History', () => {
    it('should save search queries to history', async () => {
      const { getByPlaceholderText } = render(
        <SearchProvider>
          <EnhancedSearchSystem placeholder="Search services..." />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair salon');
      fireEvent(searchInput, 'submitEditing');

      await waitFor(() => {
        expect(AsyncStorage.setItem).toHaveBeenCalledWith(
          'search_history',
          expect.stringContaining('hair salon')
        );
      });
    });

    it('should show search history suggestions', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
        JSON.stringify([
          { query: 'hair salon', timestamp: Date.now() - 1000 },
          { query: 'massage therapy', timestamp: Date.now() - 2000 },
        ])
      );

      const { getByPlaceholderText, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            showSearchHistory={true}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent(searchInput, 'focus');

      await waitFor(() => {
        expect(getByText('hair salon')).toBeTruthy();
        expect(getByText('massage therapy')).toBeTruthy();
      });
    });

    it('should clear search history', async () => {
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem showSearchHistory={true} />
        </SearchProvider>
      );

      const clearHistoryButton = getByTestId('clear-history-button');
      fireEvent.press(clearHistoryButton);

      await waitFor(() => {
        expect(AsyncStorage.removeItem).toHaveBeenCalledWith('search_history');
      });
    });
  });

  describe('Search Analytics', () => {
    it('should track search queries', async () => {
      const { getByPlaceholderText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            enableAnalytics={true}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair salon');
      fireEvent(searchInput, 'submitEditing');

      await waitFor(() => {
        expect(mockSearchApi.trackSearch).toHaveBeenCalledWith({
          query: 'hair salon',
          timestamp: expect.any(Number),
          filters: expect.any(Object),
        });
      });
    });

    it('should provide search analytics hook', () => {
      const TestComponent = () => {
        const analytics = useSearchAnalytics();
        return null;
      };

      render(
        <SearchProvider>
          <TestComponent />
        </SearchProvider>
      );

      // Should not throw error
      expect(true).toBe(true);
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility labels', () => {
      const { getByLabelText } = render(
        <SearchProvider>
          <EnhancedSearchSystem />
        </SearchProvider>
      );

      expect(getByLabelText('Search input')).toBeTruthy();
      expect(getByLabelText('Clear search')).toBeTruthy();
      expect(getByLabelText('Search filters')).toBeTruthy();
    });

    it('should support screen readers', () => {
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem />
        </SearchProvider>
      );

      const searchInput = getByTestId('search-input');
      expect(searchInput.props.accessible).toBe(true);
      expect(searchInput.props.accessibilityRole).toBe('search');
    });

    it('should announce search results count', async () => {
      mockSearchApi.search.mockResolvedValue({
        results: [{ id: 1, title: 'Hair Salon' }],
        total_count: 1,
        has_next: false,
        has_previous: false,
      });

      const { getByPlaceholderText, getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem placeholder="Search services..." />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair');

      await waitFor(() => {
        const resultsAnnouncement = getByTestId('results-announcement');
        expect(resultsAnnouncement.props.accessibilityLiveRegion).toBe('polite');
        expect(resultsAnnouncement.props.children).toContain('1 result found');
      });
    });
  });

  describe('Performance', () => {
    it('should virtualize large result sets', async () => {
      const largeResultSet = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        title: `Service ${i}`,
        type: 'service',
      }));

      mockSearchApi.search.mockResolvedValue({
        results: largeResultSet,
        total_count: 1000,
        has_next: true,
        has_previous: false,
      });

      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            enableVirtualization={true}
          />
        </SearchProvider>
      );

      // Should use VirtualizedList for large datasets
      await waitFor(() => {
        expect(getByTestId('virtualized-results')).toBeTruthy();
      });
    });

    it('should implement infinite scroll', async () => {
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            enableInfiniteScroll={true}
          />
        </SearchProvider>
      );

      const resultsList = getByTestId('search-results-list');
      fireEvent(resultsList, 'endReached');

      await waitFor(() => {
        expect(mockSearchApi.search).toHaveBeenCalledWith(
          expect.objectContaining({ page: 2 })
        );
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle search API errors', async () => {
      mockSearchApi.search.mockRejectedValue(new Error('Network error'));

      const { getByPlaceholderText, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem placeholder="Search services..." />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair salon');

      await waitFor(() => {
        expect(getByText('Search failed. Please try again.')).toBeTruthy();
      });
    });

    it('should handle voice search permission errors', async () => {
      const { getByTestId } = render(
        <SearchProvider>
          <EnhancedSearchSystem enableVoiceSearch={true} />
        </SearchProvider>
      );

      const voiceButton = getByTestId('voice-search-button');
      fireEvent.press(voiceButton);

      // Simulate permission denied
      await act(async () => {
        fireEvent(voiceButton, 'voiceError', { error: 'Permission denied' });
      });

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Permission Required',
          'Please allow microphone access to use voice search.',
          expect.any(Array)
        );
      });
    });

    it('should handle offline scenarios', async () => {
      mockSearchApi.search.mockRejectedValue(new Error('Network request failed'));

      const { getByPlaceholderText, getByText } = render(
        <SearchProvider>
          <EnhancedSearchSystem 
            placeholder="Search services..."
            enableOfflineMode={true}
          />
        </SearchProvider>
      );

      const searchInput = getByPlaceholderText('Search services...');
      fireEvent.changeText(searchInput, 'hair salon');

      await waitFor(() => {
        expect(getByText('Searching offline results...')).toBeTruthy();
      });
    });
  });
});
