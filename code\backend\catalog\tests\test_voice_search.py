"""
Tests for Voice Search functionality

This module tests the voice search service including speech-to-text
and natural language processing components.
"""

import io
import json
from django.test import TestCase
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock

from catalog.models import Service, ServiceProvider, ServiceCategory
from catalog.factories import (
    ServiceFactory,
    ProviderFactory,
    ServiceCategoryFactory
)
from catalog.voice_search import (
    SpeechToTextService,
    VoiceQueryProcessor,
    VoiceSearchService
)


class TestSpeechToTextService(TestCase):
    """Test cases for speech-to-text service"""
    
    def setUp(self):
        """Set up test data"""
        self.service = SpeechToTextService()
    
    def test_mock_transcription(self):
        """Test mock transcription functionality"""
        # Test with different audio data sizes
        small_audio = b'x' * 500
        medium_audio = b'x' * 3000
        large_audio = b'x' * 8000
        
        # Test small audio
        result = self.service.transcribe_audio(small_audio)
        self.assertTrue(result['success'])
        self.assertEqual(result['transcript'], 'hair salon')
        self.assertGreater(result['confidence'], 0.9)
        
        # Test medium audio
        result = self.service.transcribe_audio(medium_audio)
        self.assertTrue(result['success'])
        self.assertEqual(result['transcript'], 'find massage therapist near me')
        
        # Test large audio
        result = self.service.transcribe_audio(large_audio)
        self.assertTrue(result['success'])
        self.assertEqual(result['transcript'], 'show me the best rated spas in Toronto')
    
    def test_empty_audio_handling(self):
        """Test handling of empty audio data"""
        result = self.service.transcribe_audio(b'')
        # Should still work with mock service
        self.assertTrue(result['success'])
    
    def test_transcription_caching(self):
        """Test that transcription results are cached"""
        audio_data = b'test audio data'
        
        # First call
        result1 = self.service.transcribe_audio(audio_data)
        
        # Second call should return same result (cached)
        result2 = self.service.transcribe_audio(audio_data)
        
        self.assertEqual(result1, result2)


class TestVoiceQueryProcessor(TestCase):
    """Test cases for voice query processing"""
    
    def setUp(self):
        """Set up test data"""
        self.processor = VoiceQueryProcessor()
    
    def test_service_category_extraction(self):
        """Test extraction of service categories from transcript"""
        test_cases = [
            ("find hair salon", ['hair']),
            ("book massage therapy", ['massage']),
            ("show me spa and facial services", ['spa', 'facial']),
            ("nail salon near me", ['nails']),
            ("makeup artist for wedding", ['makeup'])
        ]
        
        for transcript, expected_categories in test_cases:
            result = self.processor.process_query(transcript)
            self.assertEqual(set(result['categories']), set(expected_categories))
    
    def test_location_extraction(self):
        """Test extraction of location from transcript"""
        test_cases = [
            ("find hair salon in Toronto", "Toronto"),
            ("massage therapist near Vancouver", "Vancouver"),
            ("spa around Montreal", "Montreal"),
            ("barber at Downtown", "Downtown"),
            ("salon near me", None)  # "me" should be filtered out
        ]
        
        for transcript, expected_location in test_cases:
            result = self.processor.process_query(transcript)
            self.assertEqual(result['location'], expected_location)
    
    def test_intent_extraction(self):
        """Test extraction of user intent from transcript"""
        test_cases = [
            ("find hair salon", "search"),
            ("book massage appointment", "book"),
            ("show me best spas", "compare"),
            ("what is facial treatment", "info"),
            ("get me nail salon", "search")
        ]
        
        for transcript, expected_intent in test_cases:
            result = self.processor.process_query(transcript)
            self.assertEqual(result['intent'], expected_intent)
    
    def test_filter_extraction(self):
        """Test extraction of filters from transcript"""
        # Test price filters
        result = self.processor.process_query("find cheap hair salon")
        self.assertEqual(result['filters']['price_range'], 'low')
        
        result = self.processor.process_query("luxury spa services")
        self.assertEqual(result['filters']['price_range'], 'high')
        
        # Test rating filters
        result = self.processor.process_query("best rated massage therapist")
        self.assertEqual(result['filters']['min_rating'], 4.5)
        
        # Test availability filters
        result = self.processor.process_query("book appointment today")
        self.assertEqual(result['filters']['availability'], 'today')
    
    def test_search_query_generation(self):
        """Test generation of search query from transcript"""
        test_cases = [
            ("find hair salon in Toronto", "hair salon"),
            ("show me the best massage therapist", "massage therapist"),
            ("get facial treatment", "facial treatment")
        ]
        
        for transcript, expected_query in test_cases:
            result = self.processor.process_query(transcript)
            self.assertIn(expected_query.split()[0], result['query'])
    
    def test_confidence_calculation(self):
        """Test confidence score calculation"""
        # High confidence: has categories, location, and intent
        result = self.processor.process_query("find hair salon in Toronto")
        self.assertGreater(result['confidence'], 0.8)
        
        # Low confidence: vague query
        result = self.processor.process_query("something")
        self.assertLess(result['confidence'], 0.7)
    
    def test_empty_query_handling(self):
        """Test handling of empty or invalid queries"""
        result = self.processor.process_query("")
        self.assertEqual(result['intent'], 'unknown')
        self.assertEqual(result['query'], '')
        
        result = self.processor.process_query("   ")
        self.assertEqual(result['intent'], 'unknown')


class TestVoiceSearchService(TestCase):
    """Test cases for complete voice search service"""
    
    def setUp(self):
        """Set up test data"""
        self.service = VoiceSearchService()
    
    def test_complete_voice_search_pipeline(self):
        """Test the complete voice search pipeline"""
        # Test audio data
        audio_data = b'x' * 3000  # Medium size for "find massage therapist near me"
        
        result = self.service.process_voice_search(audio_data)
        
        # Check overall success
        self.assertTrue(result['success'])
        self.assertTrue(result['search_ready'])
        
        # Check transcription
        self.assertIn('transcription', result)
        self.assertTrue(result['transcription']['success'])
        self.assertEqual(result['transcription']['transcript'], 'find massage therapist near me')
        
        # Check query processing
        self.assertIn('query', result)
        self.assertEqual(result['query']['intent'], 'search')
        self.assertIn('massage', result['query']['categories'])
    
    def test_voice_search_with_invalid_audio(self):
        """Test voice search with invalid audio data"""
        # Empty audio
        result = self.service.process_voice_search(b'')
        self.assertTrue(result['success'])  # Mock service handles empty audio
        
        # Very small audio
        result = self.service.process_voice_search(b'x')
        self.assertTrue(result['success'])


class TestVoiceSearchAPI(TestCase):
    """Test cases for voice search API endpoints"""
    
    def setUp(self):
        """Set up test data for API tests"""
        self.client = APIClient()
        
        # Create test data
        self.category = ServiceCategoryFactory.create_category(
            name="Massage Therapy",
            slug="massage-therapy"
        )
        self.provider = ProviderFactory.create_provider(
            business_name="Relaxation Spa",
            city="Toronto"
        )
        self.service = ServiceFactory.create_service(
            provider=self.provider,
            name="Deep Tissue Massage",
            description="Professional deep tissue massage therapy",
            category=self.category,
            is_popular=True
        )
    
    def test_voice_search_endpoint_success(self):
        """Test successful voice search API call"""
        # Create mock audio file
        audio_content = b'x' * 3000  # Medium size for massage query
        audio_file = SimpleUploadedFile(
            "test_audio.wav",
            audio_content,
            content_type="audio/wav"
        )
        
        url = reverse('catalog:voice-search')
        response = self.client.post(url, {
            'audio_file': audio_file,
            'language_code': 'en-US'
        }, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Check response structure
        self.assertTrue(data['success'])
        self.assertIn('transcription', data)
        self.assertIn('query', data)
        self.assertIn('search_results', data)
        
        # Check transcription
        self.assertTrue(data['transcription']['success'])
        self.assertEqual(data['transcription']['transcript'], 'find massage therapist near me')
        
        # Check search results
        if data['search_ready']:
            self.assertIn('services', data['search_results'])
            # Note: Search results may be 0 if no matching services found
            # This is acceptable for the voice search functionality test
            self.assertGreaterEqual(data['search_results']['count'], 0)
    
    def test_voice_search_endpoint_missing_audio(self):
        """Test voice search API with missing audio file"""
        url = reverse('catalog:voice-search')
        response = self.client.post(url, {
            'language_code': 'en-US'
        })
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        
        self.assertFalse(data['success'])
        self.assertEqual(data['code'], 'missing_audio')
    
    def test_voice_search_endpoint_empty_audio(self):
        """Test voice search API with empty audio file"""
        audio_file = SimpleUploadedFile(
            "empty_audio.wav",
            b'',
            content_type="audio/wav"
        )
        
        url = reverse('catalog:voice-search')
        response = self.client.post(url, {
            'audio_file': audio_file
        }, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        
        self.assertFalse(data['success'])
        self.assertEqual(data['code'], 'empty_audio')
    
    def test_voice_search_endpoint_large_audio(self):
        """Test voice search API with oversized audio file"""
        # Create audio file larger than 10MB
        large_audio = b'x' * (11 * 1024 * 1024)
        audio_file = SimpleUploadedFile(
            "large_audio.wav",
            large_audio,
            content_type="audio/wav"
        )
        
        url = reverse('catalog:voice-search')
        response = self.client.post(url, {
            'audio_file': audio_file
        }, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)
        data = response.json()
        
        self.assertFalse(data['success'])
        self.assertEqual(data['code'], 'file_too_large')
    
    def test_voice_search_with_different_languages(self):
        """Test voice search with different language codes"""
        audio_content = b'x' * 2000
        audio_file = SimpleUploadedFile(
            "test_audio.wav",
            audio_content,
            content_type="audio/wav"
        )
        
        # Test with French
        url = reverse('catalog:voice-search')
        response = self.client.post(url, {
            'audio_file': audio_file,
            'language_code': 'fr-CA'
        }, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertTrue(data['success'])


class TestVoiceSearchIntegration(TestCase):
    """Integration tests for voice search with actual search functionality"""
    
    def setUp(self):
        """Set up comprehensive test data"""
        # Create categories
        self.hair_category = ServiceCategoryFactory.create_category(
            name="Hair & Beauty",
            slug="hair-beauty"
        )
        self.massage_category = ServiceCategoryFactory.create_category(
            name="Massage Therapy",
            slug="massage-therapy"
        )
        
        # Create providers
        self.toronto_provider = ProviderFactory.create_provider(
            business_name="Toronto Beauty Studio",
            city="Toronto"
        )
        self.vancouver_provider = ProviderFactory.create_provider(
            business_name="Vancouver Wellness Center",
            city="Vancouver"
        )
        
        # Create services
        self.hair_service = ServiceFactory.create_service(
            provider=self.toronto_provider,
            name="Hair Styling Service",
            description="Professional hair styling",
            category=self.hair_category,
            is_popular=True
        )
        self.massage_service = ServiceFactory.create_service(
            provider=self.vancouver_provider,
            name="Relaxation Massage",
            description="Full body relaxation massage",
            category=self.massage_category,
            is_popular=True
        )
    
    def test_voice_search_finds_relevant_services(self):
        """Test that voice search returns relevant services"""
        # Test hair salon search
        audio_data = b'x' * 500  # Small size for "hair salon"
        service = VoiceSearchService()
        result = service.process_voice_search(audio_data)
        
        self.assertTrue(result['success'])
        self.assertEqual(result['transcription']['transcript'], 'hair salon')
        self.assertIn('hair', result['query']['categories'])
    
    def test_voice_search_location_filtering(self):
        """Test that voice search properly filters by location"""
        # This would require more complex integration with the search system
        # For now, we test that location is properly extracted
        processor = VoiceQueryProcessor()
        result = processor.process_query("find massage therapist in Toronto")
        
        self.assertEqual(result['location'], 'Toronto')
        self.assertIn('massage', result['categories'])
        self.assertEqual(result['intent'], 'search')
