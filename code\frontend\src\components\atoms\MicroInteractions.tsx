/**
 * MicroInteractions Component
 * Provides micro-interaction library with animations, haptics, and feedback
 * Supports various animation types, haptic feedback, and accessibility
 */

import React, { 
  createContext, 
  useContext, 
  useEffect, 
  useRef, 
  useState,
  useCallback,
  ComponentType,
} from 'react';
import {
  View,
  Pressable,
  AccessibilityInfo,
  ViewStyle,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withRepeat,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Types
export type AnimationType = 
  | 'scale' 
  | 'bounce' 
  | 'shake' 
  | 'pulse' 
  | 'rotate' 
  | 'slide'
  | AnimationType[];

export type HapticType = 
  | 'light' 
  | 'medium' 
  | 'heavy' 
  | 'success' 
  | 'warning' 
  | 'error'
  | false;

export type TriggerType = 'press' | 'longPress' | 'hover' | 'focus' | 'error';

export interface AnimationConfig {
  duration?: number;
  easing?: string;
  scale?: number;
  rotation?: number;
  translateX?: number;
  translateY?: number;
  damping?: number;
  stiffness?: number;
}

export interface MicroInteractionConfig {
  type?: AnimationType;
  haptic?: HapticType;
  trigger?: TriggerType;
  config?: AnimationConfig;
  continuous?: boolean;
  useNativeDriver?: boolean;
  disabled?: boolean;
}

export interface MicroInteractionsProps extends MicroInteractionConfig {
  children: React.ReactNode;
  onPress?: () => void;
  onLongPress?: () => void;
  onHover?: () => void;
  onFocus?: () => void;
  onError?: () => void;
  style?: ViewStyle;
  testID?: string;
  accessibilityLabel?: string;
  accessibilityRole?: string;
}

export interface GlobalMicroInteractionConfig {
  defaultHaptic?: HapticType;
  animationDuration?: number;
  disabled?: boolean;
  respectReducedMotion?: boolean;
}

// Context
const MicroInteractionContext = createContext<GlobalMicroInteractionConfig>({});

// Default configurations
const DEFAULT_ANIMATION_CONFIG: AnimationConfig = {
  duration: 150,
  easing: 'ease-out',
  scale: 0.95,
  damping: 15,
  stiffness: 300,
};

const ANIMATION_CONFIGS: Record<string, AnimationConfig> = {
  scale: { ...DEFAULT_ANIMATION_CONFIG, scale: 0.95 },
  bounce: { ...DEFAULT_ANIMATION_CONFIG, scale: 1.1, damping: 8 },
  shake: { ...DEFAULT_ANIMATION_CONFIG, translateX: 10, duration: 100 },
  pulse: { ...DEFAULT_ANIMATION_CONFIG, scale: 1.05, duration: 800 },
  rotate: { ...DEFAULT_ANIMATION_CONFIG, rotation: 5 },
  slide: { ...DEFAULT_ANIMATION_CONFIG, translateX: 5 },
};

// Utility functions
const triggerHaptic = async (hapticType: HapticType) => {
  if (hapticType === false) return;

  try {
    switch (hapticType) {
      case 'light':
        await Haptics.impactAsync('light');
        break;
      case 'medium':
        await Haptics.impactAsync('medium');
        break;
      case 'heavy':
        await Haptics.impactAsync('heavy');
        break;
      case 'success':
        await Haptics.notificationAsync('success');
        break;
      case 'warning':
        await Haptics.notificationAsync('warning');
        break;
      case 'error':
        await Haptics.notificationAsync('error');
        break;
    }
  } catch (error) {
    console.warn('Haptic feedback unavailable:', error);
  }
};

const getEasing = (easingName: string) => {
  switch (easingName) {
    case 'ease-in': return Easing.in(Easing.ease);
    case 'ease-out': return Easing.out(Easing.ease);
    case 'ease-in-out': return Easing.inOut(Easing.ease);
    case 'linear': return Easing.linear;
    default: return Easing.out(Easing.ease);
  }
};

// Custom hook
export const useMicroInteraction = (
  type: AnimationType = 'scale',
  config: AnimationConfig = {}
) => {
  const globalConfig = useContext(MicroInteractionContext);
  const [isReducedMotion, setIsReducedMotion] = useState(false);
  
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  useEffect(() => {
    if (globalConfig.respectReducedMotion !== false) {
      AccessibilityInfo.isReduceMotionEnabled().then(setIsReducedMotion);
    }
  }, [globalConfig.respectReducedMotion]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotate: `${rotation.value}deg` },
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  const trigger = useCallback(() => {
    if (isReducedMotion || globalConfig.disabled) return;

    const animationTypes = Array.isArray(type) ? type : [type];
    
    animationTypes.forEach((animType, index) => {
      const animConfig = { ...ANIMATION_CONFIGS[animType], ...config };
      const delay = index * 100; // Stagger chained animations

      setTimeout(() => {
        switch (animType) {
          case 'scale':
            scale.value = withSequence(
              withTiming(animConfig.scale || 0.95, { 
                duration: animConfig.duration || 150,
                easing: getEasing(animConfig.easing || 'ease-out'),
              }),
              withTiming(1, { 
                duration: animConfig.duration || 150,
                easing: getEasing(animConfig.easing || 'ease-out'),
              })
            );
            break;
            
          case 'bounce':
            scale.value = withSequence(
              withSpring(animConfig.scale || 1.1, {
                damping: animConfig.damping || 8,
                stiffness: animConfig.stiffness || 300,
              }),
              withSpring(1, {
                damping: animConfig.damping || 15,
                stiffness: animConfig.stiffness || 300,
              })
            );
            break;
            
          case 'shake':
            translateX.value = withSequence(
              withTiming(animConfig.translateX || 10, { duration: 50 }),
              withTiming(-(animConfig.translateX || 10), { duration: 50 }),
              withTiming(animConfig.translateX || 10, { duration: 50 }),
              withTiming(0, { duration: 50 })
            );
            break;
            
          case 'pulse':
            scale.value = withRepeat(
              withSequence(
                withTiming(animConfig.scale || 1.05, { 
                  duration: animConfig.duration || 800 
                }),
                withTiming(1, { 
                  duration: animConfig.duration || 800 
                })
              ),
              -1,
              true
            );
            break;
            
          case 'rotate':
            rotation.value = withSequence(
              withTiming(animConfig.rotation || 5, { 
                duration: animConfig.duration || 150 
              }),
              withTiming(0, { 
                duration: animConfig.duration || 150 
              })
            );
            break;
            
          case 'slide':
            translateX.value = withSequence(
              withTiming(animConfig.translateX || 5, { 
                duration: animConfig.duration || 150 
              }),
              withTiming(0, { 
                duration: animConfig.duration || 150 
              })
            );
            break;
        }
      }, delay);
    });
  }, [type, config, isReducedMotion, globalConfig.disabled, scale, rotation, translateX, translateY]);

  return { animatedStyle, trigger };
};

// Main component
export const MicroInteractions: React.FC<MicroInteractionsProps> = ({
  children,
  type = 'scale',
  haptic = 'light',
  trigger = 'press',
  config = {},
  continuous = false,
  useNativeDriver = true,
  disabled = false,
  onPress,
  onLongPress,
  onHover,
  onFocus,
  onError,
  style,
  testID,
  accessibilityLabel,
  accessibilityRole,
}) => {
  const globalConfig = useContext(MicroInteractionContext);
  const { animatedStyle, trigger: triggerAnimation } = useMicroInteraction(type, config);
  const [isReducedMotion, setIsReducedMotion] = useState(false);

  const effectiveHaptic = haptic !== undefined ? haptic : (globalConfig.defaultHaptic || 'light');
  const isDisabled = disabled || globalConfig.disabled;

  useEffect(() => {
    if (globalConfig.respectReducedMotion !== false) {
      AccessibilityInfo.isReduceMotionEnabled().then(setIsReducedMotion);
    }
  }, [globalConfig.respectReducedMotion]);

  useEffect(() => {
    if (continuous && !isDisabled && !isReducedMotion) {
      triggerAnimation();
    }
  }, [continuous, isDisabled, isReducedMotion, triggerAnimation]);

  const handleInteraction = useCallback(async (interactionType: TriggerType) => {
    if (isDisabled) return;

    // Always trigger animation and haptics for press events, unless disabled by reduced motion
    if (trigger === interactionType || (interactionType === 'press' && trigger === 'press')) {
      if (!isReducedMotion) {
        triggerAnimation();
      }

      if (effectiveHaptic !== false) {
        await triggerHaptic(effectiveHaptic);
      }
    }

    // Call appropriate callback
    switch (interactionType) {
      case 'press':
        onPress?.();
        break;
      case 'longPress':
        onLongPress?.();
        break;
      case 'hover':
        onHover?.();
        break;
      case 'focus':
        onFocus?.();
        break;
      case 'error':
        onError?.();
        break;
    }
  }, [isDisabled, isReducedMotion, trigger, triggerAnimation, effectiveHaptic, onPress, onLongPress, onHover, onFocus, onError]);

  const AnimatedView = Animated.createAnimatedComponent(View);

  return (
    <AnimatedView
      style={[animatedStyle, style]}
      testID={testID}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole={accessibilityRole}
      onStartShouldSetResponder={() => true}
      onResponderGrant={() => handleInteraction('press')}
      onResponderLongPress={() => handleInteraction('longPress')}
      onPanGestureEvent={(event: any) => {
        if (event.nativeEvent && trigger === 'error') {
          handleInteraction('error');
        }
      }}
    >
      {children}
    </AnimatedView>
  );
};

// HOC
export const withMicroInteraction = <P extends object>(
  Component: ComponentType<P>,
  defaultConfig: MicroInteractionConfig = {}
) => {
  return React.forwardRef<any, P & { microInteraction?: MicroInteractionConfig }>((props, ref) => {
    const { microInteraction, onPress, ...componentProps } = props as any;
    const config = { ...defaultConfig, ...microInteraction };

    return (
      <MicroInteractions {...config} onPress={onPress}>
        <Component {...(componentProps as P)} ref={ref} />
      </MicroInteractions>
    );
  });
};

// Provider
export const MicroInteractionProvider: React.FC<{
  children: React.ReactNode;
  config: GlobalMicroInteractionConfig;
}> = ({ children, config }) => {
  return (
    <MicroInteractionContext.Provider value={config}>
      {children}
    </MicroInteractionContext.Provider>
  );
};
