/**
 * SmartCache System
 * Intelligent caching system with TTL, LRU eviction, analytics, and persistence
 * Provides comprehensive caching capabilities for React Native applications
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types
export interface CacheEntry<T = any> {
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  tags?: string[];
  dependencies?: string[];
  priority?: 'low' | 'medium' | 'high';
  compressed?: boolean;
}

export interface CacheOptions {
  ttl?: number;
  tags?: string[];
  dependencies?: string[];
  priority?: 'low' | 'medium' | 'high';
  compress?: boolean;
}

export interface SmartCacheConfig {
  maxSize?: number;
  defaultTTL?: number;
  enableAnalytics?: boolean;
  enablePersistence?: boolean;
  persistenceKey?: string;
  enableMemoryPressureHandling?: boolean;
  enableMemoryOptimization?: boolean;
}

export interface CacheStats {
  size: number;
  maxSize: number;
  hits: number;
  misses: number;
  sets: number;
  invalidations: number;
  hitRate: number;
  memoryUsage: number;
}

export interface CacheAnalytics {
  hitRate: number;
  missRate: number;
  totalOperations: number;
  averageResponseTime: number;
  topKeys: Array<{ key: string; accessCount: number }>;
  evictionRate: number;
  memoryEfficiency: number;
}

export interface BatchOperation {
  key: string;
  value: any;
  options?: CacheOptions;
}

// SmartCache Class
export class SmartCache {
  private cache = new Map<string, CacheEntry>();
  private config: Required<SmartCacheConfig>;
  private stats: CacheStats;
  private accessOrder: string[] = [];
  private operationTimes: number[] = [];

  constructor(config: SmartCacheConfig = {}) {
    this.config = {
      maxSize: config.maxSize || 50,
      defaultTTL: config.defaultTTL || 300000, // 5 minutes
      enableAnalytics: config.enableAnalytics !== false,
      enablePersistence: config.enablePersistence || false,
      persistenceKey: config.persistenceKey || 'default',
      enableMemoryPressureHandling: config.enableMemoryPressureHandling || false,
      enableMemoryOptimization: config.enableMemoryOptimization || false,
    };

    this.stats = {
      size: 0,
      maxSize: this.config.maxSize,
      hits: 0,
      misses: 0,
      sets: 0,
      invalidations: 0,
      hitRate: 0,
      memoryUsage: 0,
    };

    if (this.config.enablePersistence) {
      this.restore();
    }
  }

  async get<T = any>(key: string): Promise<T | null> {
    const startTime = Date.now();
    
    try {
      const entry = this.cache.get(key);
      
      if (!entry) {
        this.updateStats('miss');
        return null;
      }

      // Check TTL expiration
      if (Date.now() - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        this.removeFromAccessOrder(key);
        this.updateStats('miss');
        return null;
      }

      // Update access information
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.updateAccessOrder(key);
      
      this.updateStats('hit');
      return entry.value as T;
    } finally {
      if (this.config.enableAnalytics) {
        this.operationTimes.push(Date.now() - startTime);
      }
    }
  }

  async set<T = any>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    const startTime = Date.now();
    
    try {
      const ttl = options.ttl || this.config.defaultTTL;
      const entry: CacheEntry<T> = {
        value,
        timestamp: Date.now(),
        ttl,
        accessCount: 0,
        lastAccessed: Date.now(),
        tags: options.tags,
        dependencies: options.dependencies,
        priority: options.priority || 'medium',
        compressed: options.compress,
      };

      // Handle compression if enabled
      if (options.compress) {
        entry.value = this.compress(value) as T;
        entry.compressed = true;
      }

      // Check if we need to evict entries
      if (this.cache.size >= this.config.maxSize && !this.cache.has(key)) {
        this.evictLRU();
      }

      this.cache.set(key, entry);
      this.updateAccessOrder(key);
      this.updateStats('set');

      if (this.config.enablePersistence) {
        await this.persist();
      }
    } finally {
      if (this.config.enableAnalytics) {
        this.operationTimes.push(Date.now() - startTime);
      }
    }
  }

  async invalidate(key: string): Promise<void> {
    const entry = this.cache.get(key);
    if (entry) {
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      this.updateStats('invalidation');

      // Invalidate entries that depend on this key
      const dependentKeys: string[] = [];
      for (const [cacheKey, cacheEntry] of this.cache.entries()) {
        if (cacheEntry.dependencies?.includes(key)) {
          dependentKeys.push(cacheKey);
        }
      }

      // Invalidate dependent entries
      for (const dependentKey of dependentKeys) {
        await this.invalidate(dependentKey);
      }

      if (this.config.enablePersistence) {
        await this.persist();
      }
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    const regex = new RegExp(pattern.replace('*', '.*'));
    const keysToInvalidate: string[] = [];

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToInvalidate.push(key);
      }
    }

    for (const key of keysToInvalidate) {
      await this.invalidate(key);
    }
  }

  async invalidateByTag(tag: string): Promise<void> {
    const keysToInvalidate: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags?.includes(tag)) {
        keysToInvalidate.push(key);
      }
    }

    for (const key of keysToInvalidate) {
      await this.invalidate(key);
    }
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.accessOrder = [];
    this.stats.size = 0;
    this.stats.memoryUsage = 0;

    if (this.config.enablePersistence) {
      await AsyncStorage.removeItem(`smart-cache:${this.config.persistenceKey}`);
    }
  }

  async warmup(data: Record<string, any>): Promise<void> {
    for (const [key, value] of Object.entries(data)) {
      await this.set(key, value);
    }
  }

  async setBatch(operations: BatchOperation[]): Promise<void> {
    for (const operation of operations) {
      await this.set(operation.key, operation.value, operation.options);
    }
  }

  async getBatch(keys: string[]): Promise<Record<string, any>> {
    const results: Record<string, any> = {};
    
    for (const key of keys) {
      const value = await this.get(key);
      if (value !== null) {
        results[key] = value;
      }
    }

    return results;
  }

  getStats(): CacheStats {
    this.stats.size = this.cache.size;
    this.stats.hitRate = this.stats.hits + this.stats.misses > 0 
      ? this.stats.hits / (this.stats.hits + this.stats.misses) 
      : 0;
    this.stats.memoryUsage = this.calculateMemoryUsage();
    return { ...this.stats };
  }

  getAnalytics(): CacheAnalytics {
    const stats = this.getStats();
    const totalOps = stats.hits + stats.misses;
    
    return {
      hitRate: stats.hitRate,
      missRate: 1 - stats.hitRate,
      totalOperations: totalOps,
      averageResponseTime: this.operationTimes.length > 0 
        ? this.operationTimes.reduce((a, b) => a + b, 0) / this.operationTimes.length 
        : 0,
      topKeys: this.getTopKeys(),
      evictionRate: stats.sets > 0 ? (stats.sets - stats.size) / stats.sets : 0,
      memoryEfficiency: stats.memoryUsage > 0 ? stats.size / stats.memoryUsage : 0,
    };
  }

  async optimizeMemory(): Promise<void> {
    if (!this.config.enableMemoryOptimization) return;

    // Remove expired entries
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        await this.invalidate(key);
      }
    }

    // Evict low-priority entries if needed
    if (this.cache.size > this.config.maxSize * 0.8) {
      const lowPriorityKeys = Array.from(this.cache.entries())
        .filter(([, entry]) => entry.priority === 'low')
        .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
        .slice(0, Math.floor(this.config.maxSize * 0.2))
        .map(([key]) => key);

      for (const key of lowPriorityKeys) {
        await this.invalidate(key);
      }
    }
  }

  async restore(): Promise<void> {
    try {
      const persistedData = await AsyncStorage.getItem(`smart-cache:${this.config.persistenceKey}`);
      if (persistedData) {
        const data = JSON.parse(persistedData);
        for (const [key, entry] of Object.entries(data)) {
          this.cache.set(key, entry as CacheEntry);
          this.accessOrder.push(key);
        }
      }
    } catch (error) {
      console.warn('Failed to restore cache from persistence:', error);
    }
  }

  private async persist(): Promise<void> {
    try {
      const data = Object.fromEntries(this.cache.entries());
      await AsyncStorage.setItem(
        `smart-cache:${this.config.persistenceKey}`,
        JSON.stringify(data)
      );
    } catch (error) {
      console.warn('Failed to persist cache:', error);
    }
  }

  private evictLRU(): void {
    if (this.accessOrder.length > 0) {
      const lruKey = this.accessOrder[0];
      this.cache.delete(lruKey);
      this.removeFromAccessOrder(lruKey);
    }
  }

  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  private updateStats(operation: 'hit' | 'miss' | 'set' | 'invalidation'): void {
    if (!this.config.enableAnalytics) return;

    switch (operation) {
      case 'hit':
        this.stats.hits++;
        break;
      case 'miss':
        this.stats.misses++;
        break;
      case 'set':
        this.stats.sets++;
        break;
      case 'invalidation':
        this.stats.invalidations++;
        break;
    }
  }

  private calculateMemoryUsage(): number {
    let usage = 0;
    for (const entry of this.cache.values()) {
      usage += JSON.stringify(entry).length;
    }
    return usage;
  }

  private getTopKeys(): Array<{ key: string; accessCount: number }> {
    return Array.from(this.cache.entries())
      .map(([key, entry]) => ({ key, accessCount: entry.accessCount }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, 10);
  }

  private compress<T>(value: T): T {
    // Simple compression simulation - in real implementation, use actual compression
    return value;
  }

  private decompress<T>(value: T): T {
    // Simple decompression simulation
    return value;
  }
}

// Context
const SmartCacheContext = createContext<{
  cache: SmartCache | null;
}>({ cache: null });

// Provider
export const SmartCacheProvider: React.FC<{
  children: React.ReactNode;
  config?: SmartCacheConfig;
}> = ({ children, config = {} }) => {
  const [cache] = useState(() => new SmartCache(config));

  return (
    <SmartCacheContext.Provider value={{ cache }}>
      {children}
    </SmartCacheContext.Provider>
  );
};

// Hook
export const useSmartCache = <T = any>(
  key?: string,
  options: {
    fetcher?: () => Promise<T>;
    ttl?: number;
    tags?: string[];
    dependencies?: string[];
  } = {}
) => {
  const { cache } = useContext(SmartCacheContext);
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    if (!cache || !key) return;

    setIsLoading(true);
    setError(null);

    try {
      // Try to get from cache first
      const cachedData = await cache.get<T>(key);
      if (cachedData !== null) {
        setData(cachedData);
        setIsLoading(false);
        return;
      }

      // Fetch new data if fetcher is provided
      if (options.fetcher) {
        const freshData = await options.fetcher();
        await cache.set(key, freshData, {
          ttl: options.ttl,
          tags: options.tags,
          dependencies: options.dependencies,
        });
        setData(freshData);
      }
    } catch (err) {
      setError(err as Error);
      setData(null);
    } finally {
      setIsLoading(false);
    }
  }, [cache, key, options.fetcher, options.ttl, options.tags, options.dependencies]);

  const set = useCallback(async (value: T, cacheOptions?: CacheOptions) => {
    if (!cache || !key) return;
    await cache.set(key, value, cacheOptions);
    setData(value);
  }, [cache, key]);

  const invalidate = useCallback(async () => {
    if (!cache || !key) return;
    await cache.invalidate(key);
    setData(null);
  }, [cache, key]);

  const refresh = useCallback(async () => {
    if (!cache || !key) return;
    await cache.invalidate(key);
    await fetchData();
  }, [cache, key, fetchData]);

  useEffect(() => {
    if (key) {
      fetchData();
    }
  }, [key, fetchData]);

  return {
    data,
    isLoading,
    error,
    set,
    invalidate,
    refresh,
    cache,
  };
};

// Analytics component
export const CacheAnalytics: React.FC<{
  cache: SmartCache;
  children: (analytics: CacheAnalytics) => React.ReactNode;
}> = ({ cache, children }) => {
  const [analytics, setAnalytics] = useState<CacheAnalytics>(cache.getAnalytics());

  useEffect(() => {
    const interval = setInterval(() => {
      setAnalytics(cache.getAnalytics());
    }, 1000);

    return () => clearInterval(interval);
  }, [cache]);

  return <>{children(analytics)}</>;
};
