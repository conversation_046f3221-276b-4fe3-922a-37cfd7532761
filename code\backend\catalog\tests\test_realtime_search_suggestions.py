"""
Tests for Real-time Search Suggestions

This module contains comprehensive tests for the real-time search suggestions system,
including debouncing, suggestion ranking, and performance validation.
"""

import pytest
from django.test import TestCase
from django.core.cache import cache
from unittest.mock import patch, MagicMock
import time
import asyncio
from decimal import Decimal

from catalog.models import Service, ServiceProvider, ServiceCategory
from catalog.factories import (
    ServiceFactory,
    ProviderFactory,
    ServiceCategoryFactory
)


class TestRealtimeSearchSuggestions(TestCase):
    """Test cases for real-time search suggestions"""

    def setUp(self):
        """Set up test data for search suggestions"""
        from catalog.search_algorithms import SearchSuggestionEngine
        self.suggestion_engine = SearchSuggestionEngine()

        # Create test categories
        self.beauty_category = ServiceCategoryFactory.create_category(
            name="Beauty & Wellness",
            slug="beauty-wellness"
        )
        self.fitness_category = ServiceCategoryFactory.create_category(
            name="Fitness & Health",
            slug="fitness-health"
        )
        self.spa_category = ServiceCategoryFactory.create_category(
            name="Spa & Relaxation",
            slug="spa-relaxation"
        )

        # Create test providers
        self.beauty_provider = ProviderFactory.create_provider(
            business_name="Elite Beauty Studio",
            city="Toronto",
            category_slug="beauty-wellness"
        )
        self.fitness_provider = ProviderFactory.create_provider(
            business_name="FitLife Gym",
            city="Vancouver",
            category_slug="fitness-health"
        )
        self.spa_provider = ProviderFactory.create_provider(
            business_name="Zen Spa Retreat",
            city="Montreal",
            category_slug="spa-relaxation"
        )

        # Create test services with varying popularity
        self.popular_services = []
        self.regular_services = []

        # Popular services
        for i in range(5):
            service = ServiceFactory.create_service(
                provider=self.beauty_provider,
                name=f"Popular Hair Service {i}",
                description=f"Popular hair styling service {i}",
                category=self.beauty_category,
                is_popular=True,
                booking_count=100 + i * 10
            )
            self.popular_services.append(service)

        # Regular services
        for i in range(10):
            service = ServiceFactory.create_service(
                provider=self.fitness_provider,
                name=f"Fitness Training {i}",
                description=f"Personal fitness training session {i}",
                category=self.fitness_category,
                is_popular=False,
                booking_count=i * 5
            )
            self.regular_services.append(service)

        # Spa services
        self.spa_services = []
        spa_names = ["Massage Therapy", "Facial Treatment", "Body Wrap", "Aromatherapy", "Hot Stone"]
        for i, name in enumerate(spa_names):
            service = ServiceFactory.create_service(
                provider=self.spa_provider,
                name=name,
                description=f"Relaxing {name.lower()} service",
                category=self.spa_category,
                is_popular=i < 2,  # First 2 are popular
                booking_count=50 - i * 5
            )
            self.spa_services.append(service)

        # Initialize suggestion engine
        self.suggestion_engine.initialize_suggestions()

    def test_basic_search_suggestions(self):
        """Test basic search suggestion functionality"""
        # Test partial match suggestions
        suggestions = self.suggestion_engine.get_suggestions("hair")
        self.assertGreater(len(suggestions), 0)

        # Verify suggestions contain relevant services
        suggestion_texts = [s['text'] for s in suggestions]
        self.assertTrue(any('hair' in text.lower() for text in suggestion_texts))

    def test_suggestion_ranking_by_popularity(self):
        """Test that suggestions are ranked by popularity"""
        suggestions = self.suggestion_engine.get_suggestions("hair")

        # Popular services should appear first
        if len(suggestions) >= 2:
            # Check that popular services have higher scores
            popular_suggestions = [s for s in suggestions if s.get('is_popular', False)]
            regular_suggestions = [s for s in suggestions if not s.get('is_popular', False)]

            if popular_suggestions and regular_suggestions:
                self.assertGreater(
                    popular_suggestions[0]['score'],
                    regular_suggestions[0]['score']
                )

    def test_suggestion_limit(self):
        """Test that suggestions respect the limit parameter"""
        # Test default limit
        suggestions = self.suggestion_engine.get_suggestions("service")
        self.assertLessEqual(len(suggestions), 10)  # Default limit

        # Test custom limit
        limited_suggestions = self.suggestion_engine.get_suggestions("service", limit=3)
        self.assertLessEqual(len(limited_suggestions), 3)

    def test_empty_query_handling(self):
        """Test handling of empty or invalid queries"""
        # Empty string
        suggestions = self.suggestion_engine.get_suggestions("")
        self.assertEqual(len(suggestions), 0)

        # None
        suggestions = self.suggestion_engine.get_suggestions(None)
        self.assertEqual(len(suggestions), 0)

        # Whitespace only
        suggestions = self.suggestion_engine.get_suggestions("   ")
        self.assertEqual(len(suggestions), 0)

    def test_suggestion_caching(self):
        """Test that suggestions are properly cached"""
        query = "massage"

        # Clear cache first
        cache.clear()

        # First call - should cache the result
        start_time = time.time()
        suggestions1 = self.suggestion_engine.get_suggestions(query)
        first_call_time = time.time() - start_time

        # Second call - should be faster due to caching
        start_time = time.time()
        suggestions2 = self.suggestion_engine.get_suggestions(query)
        second_call_time = time.time() - start_time

        # Results should be identical
        self.assertEqual(suggestions1, suggestions2)

        # Second call should be faster (cached)
        self.assertLess(second_call_time, first_call_time * 0.8)  # At least 20% faster

    def test_suggestion_debouncing(self):
        """Test debouncing functionality for rapid queries"""
        with patch.object(self.suggestion_engine, '_generate_suggestions') as mock_generate:
            mock_generate.return_value = [{'text': 'test', 'score': 1.0}]

            # Simulate rapid typing
            queries = ["h", "ha", "hai", "hair"]

            for query in queries:
                self.suggestion_engine.get_suggestions_with_debounce(query, debounce_ms=100)
                time.sleep(0.05)  # 50ms between calls

            # Should have been called fewer times due to debouncing
            self.assertLess(mock_generate.call_count, len(queries))

    def test_contextual_suggestions(self):
        """Test contextual suggestions based on category"""
        # Test category-specific suggestions
        beauty_suggestions = self.suggestion_engine.get_suggestions(
            "service",
            category_filter="beauty-wellness"
        )

        # All suggestions should be from beauty category
        for suggestion in beauty_suggestions:
            if 'category' in suggestion:
                self.assertEqual(suggestion['category'], "beauty-wellness")

    def test_location_based_suggestions(self):
        """Test location-based suggestion filtering"""
        toronto_suggestions = self.suggestion_engine.get_suggestions(
            "service",
            location_filter="Toronto"
        )

        # Should include services from Toronto providers
        self.assertGreater(len(toronto_suggestions), 0)

    def test_suggestion_performance(self):
        """Test suggestion generation performance"""
        # Create many more services for performance testing
        for i in range(100):
            ServiceFactory.create_service(
                provider=self.beauty_provider,
                name=f"Performance Test Service {i}",
                description=f"Service for performance testing {i}",
                category=self.beauty_category
            )

        # Re-initialize with more data
        self.suggestion_engine.initialize_suggestions()

        # Test performance
        start_time = time.time()
        suggestions = self.suggestion_engine.get_suggestions("performance")
        end_time = time.time()

        # Should complete within reasonable time
        self.assertLess(end_time - start_time, 0.1)  # Less than 100ms
        self.assertGreater(len(suggestions), 0)

    def test_fuzzy_matching_in_suggestions(self):
        """Test fuzzy matching for typos in suggestions"""
        # Test with typos
        suggestions = self.suggestion_engine.get_suggestions("masage")  # Missing 's'

        # Should still find "massage" services
        suggestion_texts = [s['text'].lower() for s in suggestions]
        self.assertTrue(any('massage' in text for text in suggestion_texts))

    def test_suggestion_metadata(self):
        """Test that suggestions include proper metadata"""
        suggestions = self.suggestion_engine.get_suggestions("massage")

        if suggestions:
            suggestion = suggestions[0]

            # Check required fields
            required_fields = ['text', 'score', 'type']
            for field in required_fields:
                self.assertIn(field, suggestion)

            # Check optional metadata
            optional_fields = ['category', 'provider', 'price_range']
            # At least some metadata should be present
            metadata_present = any(field in suggestion for field in optional_fields)
            self.assertTrue(metadata_present)


class TestSuggestionTypes(TestCase):
    """Test different types of suggestions"""

    def setUp(self):
        """Set up test data for suggestion types"""
        from catalog.search_algorithms import SearchSuggestionEngine
        self.suggestion_engine = SearchSuggestionEngine()

        # Create test data
        self.category = ServiceCategoryFactory.create_category(
            name="Test Category",
            slug="test-category"
        )
        self.provider = ProviderFactory.create_provider(
            business_name="Test Provider",
            city="Test City"
        )
        self.service = ServiceFactory.create_service(
            provider=self.provider,
            name="Test Service",
            description="Test description",
            category=self.category
        )

        self.suggestion_engine.initialize_suggestions()

    def test_service_name_suggestions(self):
        """Test suggestions based on service names"""
        suggestions = self.suggestion_engine.get_suggestions("test service")

        service_suggestions = [s for s in suggestions if s.get('type') == 'service']
        self.assertGreater(len(service_suggestions), 0)

    def test_category_suggestions(self):
        """Test suggestions based on categories"""
        suggestions = self.suggestion_engine.get_suggestions("test category")

        category_suggestions = [s for s in suggestions if s.get('type') == 'category']
        self.assertGreater(len(category_suggestions), 0)

    def test_provider_suggestions(self):
        """Test suggestions based on provider names"""
        suggestions = self.suggestion_engine.get_suggestions("test provider")

        provider_suggestions = [s for s in suggestions if s.get('type') == 'provider']
        self.assertGreater(len(provider_suggestions), 0)

    def test_mixed_suggestion_types(self):
        """Test that suggestions include mixed types"""
        suggestions = self.suggestion_engine.get_suggestions("test")

        # Should include different types
        types_found = set(s.get('type') for s in suggestions)
        self.assertGreater(len(types_found), 1)


class TestSuggestionCaching(TestCase):
    """Test suggestion caching mechanisms"""

    def setUp(self):
        """Set up test data for caching tests"""
        from catalog.search_algorithms import SearchSuggestionEngine
        self.suggestion_engine = SearchSuggestionEngine()

        # Clear cache
        cache.clear()

    def test_cache_key_generation(self):
        """Test cache key generation for different queries"""
        key1 = self.suggestion_engine._get_cache_key("test query")
        key2 = self.suggestion_engine._get_cache_key("test query")
        key3 = self.suggestion_engine._get_cache_key("different query")

        # Same queries should have same keys
        self.assertEqual(key1, key2)

        # Different queries should have different keys
        self.assertNotEqual(key1, key3)

    def test_cache_invalidation(self):
        """Test cache invalidation when data changes"""
        query = "test"

        # Get initial suggestions
        suggestions1 = self.suggestion_engine.get_suggestions(query)

        # Simulate data change
        self.suggestion_engine.invalidate_cache()

        # Cache should be cleared
        cache_key = self.suggestion_engine._get_cache_key(query)
        cached_result = cache.get(cache_key)
        self.assertIsNone(cached_result)

    def test_cache_expiration(self):
        """Test cache expiration settings"""
        with patch('django.core.cache.cache.set') as mock_set:
            self.suggestion_engine.get_suggestions("test")

            # Verify cache.set was called with timeout
            mock_set.assert_called()
            args, kwargs = mock_set.call_args
            self.assertIn('timeout', kwargs)
            self.assertGreater(kwargs['timeout'], 0)


class TestSuggestionIntegration(TestCase):
    """Integration tests for search suggestions"""

    def setUp(self):
        """Set up integration test data"""
        from catalog.search_algorithms import SearchSuggestionEngine, AdvancedSearchAlgorithm
        self.suggestion_engine = SearchSuggestionEngine()
        self.search_algorithm = AdvancedSearchAlgorithm()

        # Create comprehensive test data
        self.category = ServiceCategoryFactory.create_category(
            name="Integration Test Category",
            slug="integration-test"
        )
        self.provider = ProviderFactory.create_provider(
            business_name="Integration Test Provider"
        )

        # Create services with different characteristics
        self.services = []
        service_names = [
            "Hair Styling and Cut",
            "Massage Therapy Session",
            "Facial Treatment Premium",
            "Nail Art Design",
            "Yoga Class Private"
        ]

        for i, name in enumerate(service_names):
            service = ServiceFactory.create_service(
                provider=self.provider,
                name=name,
                description=f"Professional {name.lower()} service",
                category=self.category,
                is_popular=i < 2,
                booking_count=50 - i * 5
            )
            self.services.append(service)

        # Initialize both engines
        self.suggestion_engine.initialize_suggestions()

    def test_suggestion_to_search_integration(self):
        """Test that suggestions lead to valid search results"""
        # Get suggestions
        suggestions = self.suggestion_engine.get_suggestions("hair")
        self.assertGreater(len(suggestions), 0)

        # Use first suggestion for actual search
        if suggestions:
            suggestion_text = suggestions[0]['text']
            search_results = self.search_algorithm.search(suggestion_text)

            # Should return valid search results
            self.assertGreater(len(search_results), 0)
            # Search results is a list of service dictionaries
            if search_results:
                self.assertIn('service', search_results[0])

    def test_suggestion_consistency_with_search(self):
        """Test that suggestions are consistent with search results"""
        query = "massage"

        # Get suggestions
        suggestions = self.suggestion_engine.get_suggestions(query)

        # Get search results
        search_results = self.search_algorithm.search(query)

        # If suggestions exist, search should also return results
        if suggestions:
            self.assertGreater(len(search_results), 0)

    def test_end_to_end_suggestion_workflow(self):
        """Test complete suggestion workflow"""
        # 1. User starts typing
        partial_queries = ["h", "ha", "hai", "hair"]

        for query in partial_queries:
            suggestions = self.suggestion_engine.get_suggestions(query)

            # Each query should return some suggestions (except maybe single char)
            if len(query) > 1:
                self.assertGreaterEqual(len(suggestions), 0)

        # 2. User selects a suggestion
        final_suggestions = self.suggestion_engine.get_suggestions("hair")
        if final_suggestions:
            selected_suggestion = final_suggestions[0]['text']

            # 3. Perform actual search
            search_results = self.search_algorithm.search(selected_suggestion)

            # Should return relevant results
            self.assertGreater(len(search_results), 0)