/**
 * MicroInteractions Component Tests
 * Tests for micro-interaction library with animations, haptics, and feedback
 * Following TDD protocol - these tests will initially fail
 */

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { View, Text, Pressable } from 'react-native';
import { 
  MicroInteractions,
  useMicroInteraction,
  withMicroInteraction,
  MicroInteractionProvider,
} from '../atoms/MicroInteractions';

// Mock Haptics
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  notificationAsync: jest.fn(),
  selectionAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy',
  },
  NotificationFeedbackType: {
    Success: 'success',
    Warning: 'warning',
    Error: 'error',
  },
}));

// Mock Animated
jest.mock('react-native-reanimated', () => {
  const View = require('react-native').View;
  return {
    default: {
      View: View,
      createAnimatedComponent: (component: any) => component,
    },
    createAnimatedComponent: (component: any) => component,
    useSharedValue: (initial: any) => ({ value: initial }),
    useAnimatedStyle: (fn: any) => ({}),
    withSpring: (value: any) => value,
    withTiming: (value: any) => value,
    withSequence: (...values: any[]) => values[values.length - 1],
    withRepeat: (value: any) => value,
    runOnJS: (fn: any) => fn,
    Easing: {
      in: (fn: any) => fn,
      out: (fn: any) => fn,
      inOut: (fn: any) => fn,
      linear: 'linear',
      ease: 'ease',
    },
  };
});

// Test components
const TestButton = ({ onPress, children, ...props }: any) => (
  <Pressable onPress={onPress} testID="test-button" {...props}>
    <Text>{children}</Text>
  </Pressable>
);

const TestCard = ({ children, ...props }: any) => (
  <View testID="test-card" {...props}>
    <Text>{children}</Text>
  </View>
);

describe('MicroInteractions Library', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('MicroInteractions Component', () => {
    it('should render children without interaction by default', () => {
      const { getByText } = render(
        <MicroInteractions>
          <Text>Test Content</Text>
        </MicroInteractions>
      );

      expect(getByText('Test Content')).toBeTruthy();
    });

    it('should apply scale animation on press', async () => {
      const onPress = jest.fn();
      
      const { getByTestId } = render(
        <MicroInteractions
          type="scale"
          onPress={onPress}
          testID="micro-interaction"
        >
          <TestButton>Press Me</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      fireEvent.press(element);

      expect(onPress).toHaveBeenCalled();
    });

    it('should apply bounce animation on press', async () => {
      const { getByTestId } = render(
        <MicroInteractions
          type="bounce"
          testID="micro-interaction"
        >
          <TestButton>Bounce Me</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      fireEvent.press(element);

      // Animation should be triggered
      await waitFor(() => {
        expect(element).toBeTruthy();
      });
    });

    it('should apply shake animation on error', async () => {
      const { getByTestId } = render(
        <MicroInteractions
          type="shake"
          trigger="error"
          testID="micro-interaction"
        >
          <TestButton>Shake on Error</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      
      // Trigger error state
      fireEvent(element, 'onError');

      await waitFor(() => {
        expect(element).toBeTruthy();
      });
    });

    it('should apply pulse animation continuously', async () => {
      const { getByTestId } = render(
        <MicroInteractions
          type="pulse"
          continuous
          testID="micro-interaction"
        >
          <TestButton>Pulsing Button</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      expect(element).toBeTruthy();

      // Should start pulsing immediately
      await waitFor(() => {
        expect(element).toBeTruthy();
      });
    });

    it('should support custom animation configuration', async () => {
      const customConfig = {
        duration: 500,
        easing: 'ease-in-out',
        scale: 1.2,
      };

      const { getByTestId } = render(
        <MicroInteractions
          type="scale"
          config={customConfig}
          testID="micro-interaction"
        >
          <TestButton>Custom Animation</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      fireEvent.press(element);

      expect(element).toBeTruthy();
    });
  });

  describe('Haptic Feedback', () => {
    it('should trigger haptic feedback on interaction', async () => {
      const Haptics = require('expo-haptics');
      
      const { getByTestId } = render(
        <MicroInteractions
          type="scale"
          haptic="medium"
          testID="micro-interaction"
        >
          <TestButton>Haptic Button</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      fireEvent(element, 'responderGrant');

      expect(Haptics.impactAsync).toHaveBeenCalledWith('medium');
    });

    it('should support different haptic intensities', async () => {
      const Haptics = require('expo-haptics');
      
      const { getByTestId } = render(
        <MicroInteractions
          type="bounce"
          haptic="heavy"
          testID="micro-interaction"
        >
          <TestButton>Heavy Haptic</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      fireEvent(element, 'responderGrant');

      expect(Haptics.impactAsync).toHaveBeenCalledWith('heavy');
    });

    it('should support notification haptics', async () => {
      const Haptics = require('expo-haptics');
      
      const { getByTestId } = render(
        <MicroInteractions
          type="scale"
          haptic="success"
          testID="micro-interaction"
        >
          <TestButton>Success Haptic</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      fireEvent(element, 'responderGrant');

      expect(Haptics.notificationAsync).toHaveBeenCalledWith('success');
    });

    it('should disable haptics when specified', async () => {
      const Haptics = require('expo-haptics');
      
      const { getByTestId } = render(
        <MicroInteractions
          type="scale"
          haptic={false}
          testID="micro-interaction"
        >
          <TestButton>No Haptic</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('micro-interaction');
      fireEvent.press(element);

      expect(Haptics.impactAsync).not.toHaveBeenCalled();
      expect(Haptics.notificationAsync).not.toHaveBeenCalled();
    });
  });

  describe('useMicroInteraction Hook', () => {
    const TestComponent = ({ type, config }: any) => {
      const { animatedStyle, trigger } = useMicroInteraction(type, config);
      
      return (
        <Pressable onPress={trigger} testID="hook-test">
          <Text>Hook Test</Text>
        </Pressable>
      );
    };

    it('should provide animated style and trigger function', () => {
      const { getByTestId } = render(
        <TestComponent type="scale" />
      );

      const element = getByTestId('hook-test');
      expect(element).toBeTruthy();
    });

    it('should trigger animation when trigger function is called', async () => {
      const { getByTestId } = render(
        <TestComponent type="bounce" />
      );

      const element = getByTestId('hook-test');
      fireEvent.press(element);

      await waitFor(() => {
        expect(element).toBeTruthy();
      });
    });

    it('should support custom configuration', () => {
      const config = { duration: 300, scale: 0.95 };
      
      const { getByTestId } = render(
        <TestComponent type="scale" config={config} />
      );

      const element = getByTestId('hook-test');
      expect(element).toBeTruthy();
    });
  });

  describe('withMicroInteraction HOC', () => {
    const EnhancedButton = withMicroInteraction(TestButton, {
      type: 'scale',
      haptic: 'light',
    });

    it('should enhance component with micro interactions', () => {
      const { getByTestId } = render(
        <EnhancedButton testID="enhanced-button">
          Enhanced Button
        </EnhancedButton>
      );

      const element = getByTestId('enhanced-button');
      expect(element).toBeTruthy();
    });

    it('should trigger interactions on enhanced component', async () => {
      const Haptics = require('expo-haptics');
      const onPress = jest.fn();

      const { getByText } = render(
        <EnhancedButton onPress={onPress}>
          Enhanced Button
        </EnhancedButton>
      );

      const element = getByText('Enhanced Button');
      fireEvent.press(element);

      expect(onPress).toHaveBeenCalled();
      expect(Haptics.impactAsync).toHaveBeenCalledWith('light');
    });

    it('should allow overriding default configuration', async () => {
      const { getByText } = render(
        <EnhancedButton
          microInteraction={{ type: 'bounce', haptic: 'heavy' }}
        >
          Override Config
        </EnhancedButton>
      );

      const element = getByText('Override Config');
      fireEvent.press(element);

      const Haptics = require('expo-haptics');
      expect(Haptics.impactAsync).toHaveBeenCalledWith('heavy');
    });
  });

  describe('MicroInteractionProvider', () => {
    it('should provide global configuration', () => {
      const globalConfig = {
        defaultHaptic: 'medium',
        animationDuration: 200,
        disabled: false,
      };

      const { getByText } = render(
        <MicroInteractionProvider config={globalConfig}>
          <Text>Provider Test</Text>
        </MicroInteractionProvider>
      );

      expect(getByText('Provider Test')).toBeTruthy();
    });

    it('should disable all interactions when specified', async () => {
      const Haptics = require('expo-haptics');
      const globalConfig = { disabled: true };

      const { getByTestId } = render(
        <MicroInteractionProvider config={globalConfig}>
          <MicroInteractions type="scale" haptic="medium" testID="disabled-interaction">
            <TestButton>Disabled Button</TestButton>
          </MicroInteractions>
        </MicroInteractionProvider>
      );

      const element = getByTestId('disabled-interaction');
      fireEvent.press(element);

      expect(Haptics.impactAsync).not.toHaveBeenCalled();
    });

    it('should apply global haptic settings', async () => {
      const Haptics = require('expo-haptics');
      const globalConfig = { defaultHaptic: 'heavy' };

      const { getByTestId } = render(
        <MicroInteractionProvider config={globalConfig}>
          <MicroInteractions type="scale" haptic={undefined} testID="global-haptic">
            <TestButton>Global Haptic</TestButton>
          </MicroInteractions>
        </MicroInteractionProvider>
      );

      const element = getByTestId('global-haptic');
      fireEvent(element, 'responderGrant');

      expect(Haptics.impactAsync).toHaveBeenCalledWith('heavy');
    });
  });

  describe('Animation Types', () => {
    const animationTypes = ['scale', 'bounce', 'shake', 'pulse', 'rotate', 'slide'];

    animationTypes.forEach(type => {
      it(`should support ${type} animation`, async () => {
        const { getByTestId } = render(
          <MicroInteractions type={type} testID="animation-test">
            <TestButton>{type} Animation</TestButton>
          </MicroInteractions>
        );

        const element = getByTestId('animation-test');
        fireEvent.press(element);

        await waitFor(() => {
          expect(element).toBeTruthy();
        });
      });
    });

    it('should support chained animations', async () => {
      const { getByTestId } = render(
        <MicroInteractions 
          type={['scale', 'bounce']} 
          testID="chained-animation"
        >
          <TestButton>Chained Animation</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('chained-animation');
      fireEvent.press(element);

      await waitFor(() => {
        expect(element).toBeTruthy();
      });
    });
  });

  describe('Performance and Accessibility', () => {
    it('should respect reduced motion preferences', async () => {
      // Mock reduced motion preference
      const mockAccessibilityInfo = {
        isReduceMotionEnabled: jest.fn().mockResolvedValue(true),
      };

      jest.doMock('react-native', () => ({
        ...jest.requireActual('react-native'),
        AccessibilityInfo: mockAccessibilityInfo,
      }));

      const { getByTestId } = render(
        <MicroInteractions type="scale" testID="reduced-motion">
          <TestButton>Reduced Motion</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('reduced-motion');
      fireEvent.press(element);

      // Animation should be disabled or reduced
      expect(element).toBeTruthy();
    });

    it('should maintain accessibility properties', () => {
      const { getByTestId } = render(
        <MicroInteractions 
          type="scale"
          accessibilityLabel="Interactive button"
          accessibilityRole="button"
          testID="accessible-interaction"
        >
          <TestButton>Accessible Button</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('accessible-interaction');
      expect(element.props.accessibilityLabel).toBe('Interactive button');
      expect(element.props.accessibilityRole).toBe('button');
    });

    it('should optimize performance with native driver', async () => {
      const { getByTestId } = render(
        <MicroInteractions 
          type="scale"
          useNativeDriver
          testID="native-driver"
        >
          <TestButton>Native Driver</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('native-driver');
      fireEvent.press(element);

      // Should use native driver for better performance
      await waitFor(() => {
        expect(element).toBeTruthy();
      });
    });

    it('should handle rapid interactions gracefully', async () => {
      const onPress = jest.fn();
      
      const { getByTestId } = render(
        <MicroInteractions type="scale" onPress={onPress} testID="rapid-interaction">
          <TestButton>Rapid Press</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('rapid-interaction');
      
      // Simulate rapid presses
      for (let i = 0; i < 10; i++) {
        fireEvent.press(element);
      }

      // Should handle all presses without crashing
      expect(onPress).toHaveBeenCalledTimes(10);
    });
  });

  describe('Error Handling', () => {
    it('should handle animation errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const { getByTestId } = render(
        <MicroInteractions 
          type="invalid-type" 
          testID="error-handling"
        >
          <TestButton>Error Test</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('error-handling');
      fireEvent.press(element);

      // Should not crash and should log error
      expect(element).toBeTruthy();
      
      consoleSpy.mockRestore();
    });

    it('should fallback when haptics are unavailable', async () => {
      const Haptics = require('expo-haptics');
      Haptics.impactAsync.mockRejectedValue(new Error('Haptics unavailable'));
      
      const { getByTestId } = render(
        <MicroInteractions type="scale" haptic="medium" testID="haptic-fallback">
          <TestButton>Haptic Fallback</TestButton>
        </MicroInteractions>
      );

      const element = getByTestId('haptic-fallback');
      fireEvent.press(element);

      // Should not crash when haptics fail
      await waitFor(() => {
        expect(element).toBeTruthy();
      });
    });
  });
});
