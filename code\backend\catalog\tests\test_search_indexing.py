"""
Search Indexing System Tests

Tests for the search indexing system including index creation, updates, 
and search performance validation.

Following TDD protocol - these tests should initially fail until implementation is complete.
"""

from django.test import TestCase
from django.core.management import call_command
from django.db import transaction
from unittest.mock import patch, MagicMock
import time

from catalog.models import Service, ServiceProvider, ServiceCategory
from catalog.factories import (
    ServiceFactory,
    ProviderFactory,
    ServiceCategoryFactory
)


class TestSearchIndexingSystem(TestCase):
    """Test search indexing system functionality"""
    
    def setUp(self):
        """Set up test data for search indexing"""
        from catalog.search_algorithms import SearchIndexManager
        self.SearchIndexManager = SearchIndexManager  # Store class for creating fresh instances
        self.index_manager = SearchIndexManager()
        
        # Create test categories
        self.beauty_category = ServiceCategoryFactory.create_category(
            name="Beauty & Wellness",
            slug="beauty-wellness"
        )
        self.fitness_category = ServiceCategoryFactory.create_category(
            name="Fitness & Health",
            slug="fitness-health"
        )
        
        # Create test providers
        self.beauty_provider = ProviderFactory.create_provider(
            business_name="Beauty Index Studio",
            category_slug="beauty-wellness"
        )
        self.fitness_provider = ProviderFactory.create_provider(
            business_name="Fitness Index Gym",
            category_slug="fitness-health"
        )
        
        # Create test services
        self.beauty_service = ServiceFactory.create_service(
            provider=self.beauty_provider,
            name="Facial Treatment",
            description="Rejuvenating facial treatment for all skin types",
            category=self.beauty_category
        )
        self.fitness_service = ServiceFactory.create_service(
            provider=self.fitness_provider,
            name="Personal Training",
            description="One-on-one fitness training sessions",
            category=self.fitness_category
        )

        # Initialize the search index with existing services
        self.index_manager.create_index()
    
    def test_index_creation(self):
        """Test that search index is created properly"""
        # Create index
        result = self.index_manager.create_index()
        
        self.assertTrue(result["success"])
        self.assertGreater(result["indexed_count"], 0)
        
        # Verify index exists
        self.assertTrue(self.index_manager.index_exists())
    
    def test_index_update_on_service_creation(self):
        """Test that index is updated when new services are created"""
        initial_count = self.index_manager.get_index_size()

        # Create new service
        new_service = ServiceFactory.create_service(
            provider=self.beauty_provider,
            name="New Indexed Service",
            description="This service should be automatically indexed",
            category=self.beauty_category
        )

        # Create a fresh index manager to get updated cache
        fresh_index_manager = self.SearchIndexManager()
        updated_count = fresh_index_manager.get_index_size()
        self.assertGreater(updated_count, initial_count)
        
        # New service should be searchable
        search_results = fresh_index_manager.search("New Indexed Service")
        self.assertGreater(len(search_results), 0)
        
        found_service = any(
            result["service_id"] == new_service.id 
            for result in search_results
        )
        self.assertTrue(found_service)
    
    def test_index_update_on_service_modification(self):
        """Test that index is updated when services are modified"""
        # Modify existing service
        original_name = self.beauty_service.name
        self.beauty_service.name = "Updated Facial Treatment Service"
        self.beauty_service.save()
        
        # Search for updated name
        search_results = self.index_manager.search("Updated Facial Treatment")
        self.assertGreater(len(search_results), 0)
        
        # Should not find old name
        old_results = self.index_manager.search(original_name)
        found_old = any(
            result["service_id"] == self.beauty_service.id 
            for result in old_results
        )
        self.assertFalse(found_old)
    
    def test_index_update_on_service_deletion(self):
        """Test that index is updated when services are deleted"""
        service_id = self.beauty_service.id
        service_name = self.beauty_service.name
        
        # Delete service
        self.beauty_service.delete()
        
        # Should not be found in search
        search_results = self.index_manager.search(service_name)
        found_deleted = any(
            result["service_id"] == service_id 
            for result in search_results
        )
        self.assertFalse(found_deleted)
    
    def test_index_performance_with_large_dataset(self):
        """Test index performance with large number of services"""
        # Create many services
        services = []
        for i in range(100):
            service = ServiceFactory.create_service(
                provider=self.beauty_provider,
                name=f"Performance Test Service {i}",
                description=f"Test service number {i} for performance testing",
                category=self.beauty_category
            )
            services.append(service)
        
        # Rebuild index
        start_time = time.time()
        result = self.index_manager.rebuild_index()
        index_time = time.time() - start_time
        
        # Should complete indexing in reasonable time (< 5 seconds for 100 services)
        self.assertLess(index_time, 5.0)
        self.assertTrue(result["success"])
        self.assertGreaterEqual(result["indexed_count"], 100)
        
        # Search should be fast
        start_time = time.time()
        search_results = self.index_manager.search("Performance Test")
        search_time = time.time() - start_time
        
        # Search should complete in < 100ms
        self.assertLess(search_time, 0.1)
        self.assertGreaterEqual(len(search_results), 50)  # Should find many matches
    
    def test_index_consistency_check(self):
        """Test index consistency validation"""
        # Check initial consistency
        consistency_report = self.index_manager.check_consistency()
        
        self.assertTrue(consistency_report["is_consistent"])
        self.assertEqual(consistency_report["missing_services"], [])
        self.assertEqual(consistency_report["orphaned_entries"], [])
    
    def test_index_rebuild_functionality(self):
        """Test complete index rebuild"""
        # Corrupt index (simulate)
        self.index_manager.clear_index()
        
        # Rebuild
        result = self.index_manager.rebuild_index()
        
        self.assertTrue(result["success"])
        self.assertGreater(result["indexed_count"], 0)
        
        # Verify all services are searchable
        for service in [self.beauty_service, self.fitness_service]:
            search_results = self.index_manager.search(service.name)
            found = any(
                result["service_id"] == service.id 
                for result in search_results
            )
            self.assertTrue(found, f"Service {service.name} not found after rebuild")
    
    def test_index_optimization(self):
        """Test index optimization for better performance"""
        # Add many services then optimize
        for i in range(50):
            ServiceFactory.create_service(
                provider=self.beauty_provider,
                name=f"Optimization Test {i}",
                description=f"Service for optimization testing {i}",
                category=self.beauty_category
            )
        
        # Optimize index
        optimization_result = self.index_manager.optimize_index()
        
        self.assertTrue(optimization_result["success"])
        self.assertGreater(optimization_result["performance_improvement"], 0)
    
    def test_index_backup_and_restore(self):
        """Test index backup and restore functionality"""
        # Create backup
        backup_result = self.index_manager.backup_index()
        self.assertTrue(backup_result["success"])
        self.assertIsNotNone(backup_result["backup_id"])
        
        # Clear index
        self.index_manager.clear_index()
        self.assertEqual(self.index_manager.get_index_size(), 0)
        
        # Restore from backup
        restore_result = self.index_manager.restore_index(backup_result["backup_id"])
        self.assertTrue(restore_result["success"])
        
        # Verify restoration
        self.assertGreater(self.index_manager.get_index_size(), 0)
        
        # Verify services are searchable
        search_results = self.index_manager.search("Facial Treatment")
        self.assertGreater(len(search_results), 0)


class TestSearchIndexManagement(TestCase):
    """Test search index management commands and utilities"""
    
    def test_rebuild_search_index_command(self):
        """Test Django management command for rebuilding search index"""
        # This should not raise an exception
        call_command('rebuild_search_index', verbosity=0)
        
        # Verify index was rebuilt
        from catalog.search_algorithms import SearchIndexManager
        index_manager = SearchIndexManager()
        self.assertTrue(index_manager.index_exists())
    
    def test_optimize_search_index_command(self):
        """Test Django management command for optimizing search index"""
        # This should not raise an exception
        call_command('optimize_search_index', verbosity=0)
    
    def test_check_search_index_command(self):
        """Test Django management command for checking search index"""
        # This should not raise an exception
        call_command('check_search_index', verbosity=0)
