"""
Django management command to check search index consistency
"""

from django.core.management.base import BaseCommand
from catalog.search_algorithms import SearchIndexManager


class Command(BaseCommand):
    help = 'Check search index consistency and health'

    def handle(self, *args, **options):
        self.stdout.write('Checking search index consistency...')

        index_manager = SearchIndexManager()

        if not index_manager.index_exists():
            self.stdout.write(
                self.style.ERROR('No search index found. Run rebuild_search_index first.')
            )
            return

        # Check consistency
        consistency_report = index_manager.check_consistency()

        if consistency_report['is_consistent']:
            self.stdout.write(
                self.style.SUCCESS('Search index is consistent with database.')
            )
        else:
            self.stdout.write(
                self.style.WARNING('Search index inconsistencies found:')
            )

            if consistency_report['missing_services']:
                self.stdout.write(
                    f'  Missing services in index: {len(consistency_report["missing_services"])}'
                )

            if consistency_report['orphaned_entries']:
                self.stdout.write(
                    f'  Orphaned entries in index: {len(consistency_report["orphaned_entries"])}'
                )

        # Display index statistics
        index_size = index_manager.get_index_size()
        self.stdout.write(f'Index size: {index_size} entries')