{"name": "frontend", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit": "jest --testPathPattern=__tests__/unit", "test:integration": "jest --testPathPattern=__tests__/integration", "test:e2e": "jest --testPathPattern=__tests__/e2e", "test:components": "jest --testPathPattern=components/__tests__", "test:auth": "jest --testPathPattern=__tests__/auth", "test:fix": "jest --testPathPattern=__tests__/fix", "test:audit": "jest --testPathPattern=__tests__/audit", "test:debug": "jest --testPathPattern=__tests__/debug --verbose", "test:clear-cache": "jest --clear<PERSON>ache"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/slider": "^4.5.7", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.2.1", "@react-navigation/native": "^7.0.15", "@react-navigation/stack": "^7.1.1", "@tanstack/react-query": "^5.62.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "expo": "~53.0.20", "expo-haptics": "^14.1.4", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "^16.1.4", "expo-location": "~18.1.6", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-image-picker": "^8.2.1", "react-native-reanimated": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.3.0", "tailwind-merge": "^3.3.1", "expo-speech": "~13.1.7", "expo-av": "~15.1.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "axios": "^1.11.0", "babel-jest": "^30.0.5", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "react-dom": "^19.0.0", "react-native-web": "^0.21.0", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}