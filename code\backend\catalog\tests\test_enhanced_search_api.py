"""
Tests for Enhanced Search API endpoints

This module tests the API endpoints for real-time search suggestions
and enhanced search functionality.
"""

from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status

from catalog.models import Service, ServiceProvider, ServiceCategory
from catalog.factories import (
    ServiceFactory,
    ProviderFactory,
    ServiceCategoryFactory
)


class TestEnhancedSearchAPI(TestCase):
    """Test cases for enhanced search API endpoints"""
    
    def setUp(self):
        """Set up test data for API tests"""
        self.client = APIClient()
        
        # Create test data
        self.category = ServiceCategoryFactory.create_category(
            name="Hair & Beauty",
            slug="hair-beauty"
        )
        self.provider = ProviderFactory.create_provider(
            business_name="Test Beauty Studio",
            city="Toronto"
        )
        self.service = ServiceFactory.create_service(
            provider=self.provider,
            name="Hair Styling Service",
            description="Professional hair styling",
            category=self.category,
            is_popular=True
        )
    
    def test_search_suggestions_endpoint(self):
        """Test the search suggestions API endpoint"""
        url = reverse('catalog:enhanced-search')
        response = self.client.get(url, {
            'q': 'hair',
            'type': 'suggestions',
            'limit': 5
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Check response structure
        self.assertIn('suggestions', data)
        self.assertIn('query', data)
        self.assertIn('count', data)
        
        # Check suggestions format
        if data['suggestions']:
            suggestion = data['suggestions'][0]
            self.assertIn('text', suggestion)
            self.assertIn('type', suggestion)
            self.assertIn('score', suggestion)
    
    def test_enhanced_service_search_endpoint(self):
        """Test the enhanced service search API endpoint"""
        url = reverse('catalog:enhanced-search')
        response = self.client.get(url, {
            'q': 'hair',
            'type': 'services',
            'limit': 10
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Check response structure
        self.assertIn('results', data)
        self.assertIn('count', data)
        self.assertIn('query', data)
        self.assertIn('type', data)
        
        # Should find our test service
        self.assertGreater(data['count'], 0)
    
    def test_enhanced_provider_search_endpoint(self):
        """Test the enhanced provider search API endpoint"""
        url = reverse('catalog:enhanced-search')
        response = self.client.get(url, {
            'q': 'beauty',
            'type': 'providers',
            'limit': 10
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Check response structure
        self.assertIn('results', data)
        self.assertIn('count', data)
        self.assertIn('query', data)
        self.assertIn('type', data)
        
        # Should find our test provider
        self.assertGreater(data['count'], 0)
    
    def test_search_with_filters(self):
        """Test search with category and location filters"""
        url = reverse('catalog:enhanced-search')
        response = self.client.get(url, {
            'q': 'hair',
            'type': 'suggestions',
            'category': 'hair-beauty',
            'location': 'Toronto',
            'limit': 5
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('suggestions', data)
        self.assertIn('query', data)
    
    def test_empty_query_handling(self):
        """Test handling of empty queries"""
        url = reverse('catalog:enhanced-search')
        response = self.client.get(url, {
            'q': '',
            'type': 'suggestions'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Should return empty suggestions for empty query
        self.assertEqual(len(data['suggestions']), 0)
    
    def test_invalid_search_type(self):
        """Test handling of invalid search types"""
        url = reverse('catalog:enhanced-search')
        response = self.client.get(url, {
            'q': 'test',
            'type': 'invalid_type'
        })
        
        # Should default to services search
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['type'], 'services')
