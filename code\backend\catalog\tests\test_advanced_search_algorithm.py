"""
Advanced Search Algorithm Tests

Tests for the advanced search algorithm with fuzzy matching capabilities.
This test suite covers:
- Fuzzy string matching for typos and partial matches
- Search result ranking and relevance scoring
- Performance optimization for large datasets
- Edge cases and error handling
- Real-time search suggestions
- Voice search integration capabilities
- Advanced filtering and ML-based recommendations

Following TDD protocol - these tests should initially fail until implementation is complete.
"""

import pytest
import random
import time
from django.test import TestCase
from django.db.models import Q
from unittest.mock import patch, MagicMock

from catalog.models import Service, ServiceProvider, ServiceCategory
from catalog.search_algorithms import (
    AdvancedSearchAlgorithm,
    FuzzyMatcher,
    SearchResultRanker,
    SearchIndexer,
    SearchSuggestionEngine,
    VoiceSearchProcessor,
    SearchIndexManager
)
from catalog.factories import ServiceFactory, ProviderFactory, ServiceCategoryFactory


class TestFuzzyMatcher(TestCase):
    """Test fuzzy string matching functionality"""
    
    def setUp(self):
        self.fuzzy_matcher = FuzzyMatcher()
    
    def test_exact_match_returns_high_score(self):
        """Test that exact matches return maximum relevance score"""
        query = "hair styling"
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertAlmostEqual(score, 1.0, places=2)
    
    def test_partial_match_returns_medium_score(self):
        """Test that partial matches return appropriate scores"""
        query = "hair"
        text = "hair styling and coloring"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.5)
        self.assertLess(score, 1.0)
    
    def test_typo_tolerance_single_character(self):
        """Test that single character typos are handled gracefully"""
        query = "hiar styling"  # typo: hiar instead of hair
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.3)  # Should still be relevant despite typo
    
    def test_typo_tolerance_multiple_characters(self):
        """Test handling of multiple character typos"""
        query = "hiar stylingg"  # multiple typos
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.1)  # Should still have some relevance
    
    def test_case_insensitive_matching(self):
        """Test that matching is case insensitive"""
        query = "HAIR STYLING"
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertEqual(score, 1.0)
    
    def test_word_order_flexibility(self):
        """Test that word order doesn't significantly impact relevance"""
        query = "styling hair"
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.4)  # Should still be relevant with different word order
    
    def test_no_match_returns_zero_score(self):
        """Test that completely unrelated text returns zero score"""
        query = "hair styling"
        text = "car repair"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertLess(score, 0.1)  # Should be very low for unrelated terms
    
    def test_empty_query_handling(self):
        """Test handling of empty queries"""
        query = ""
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertEqual(score, 0.0)
    
    def test_empty_text_handling(self):
        """Test handling of empty text"""
        query = "hair styling"
        text = ""
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertEqual(score, 0.0)

    def test_phonetic_matching(self):
        """Test phonetic matching for similar sounding words"""
        # These tests should initially fail until phonetic matching is implemented
        query = "hair"
        text = "hare"  # Sounds similar but different spelling
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.2)  # Should have some similarity due to phonetic match

        query = "massage"
        text = "masage"  # Common misspelling
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.6)  # Should be high similarity

    def test_keyboard_layout_typos(self):
        """Test handling of keyboard layout-based typos"""
        # These tests should initially fail until keyboard layout awareness is implemented
        query = "nail"
        text = "mial"  # 'n' and 'm' are adjacent on QWERTY keyboard
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.3)  # Should recognize keyboard proximity

        query = "beauty"
        text = "beautu"  # 'y' and 'u' are adjacent
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.7)  # Should be high similarity

    def test_abbreviation_expansion(self):
        """Test matching of abbreviations with full forms"""
        # These tests should initially fail until abbreviation matching is implemented
        query = "spa"
        text = "special purpose area"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.4)  # Should recognize abbreviation

        query = "apt"
        text = "appointment"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.5)  # Should match abbreviation

    def test_contextual_synonym_matching(self):
        """Test advanced contextual synonym matching"""
        # These tests should initially fail until contextual synonyms are implemented
        query = "cut"
        text = "trim hair styling"  # 'cut' and 'trim' are synonyms in hair context
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.6)  # Should recognize contextual synonym

        query = "relax"
        text = "massage therapy"  # 'relax' is contextually related to massage
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.4)  # Should have contextual relevance

    def test_multi_language_support(self):
        """Test fuzzy matching with multi-language terms"""
        # These tests should initially fail until multi-language support is implemented
        query = "belleza"  # Spanish for beauty
        text = "beauty salon"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.3)  # Should recognize cross-language similarity

        query = "coiffure"  # French for hairstyle
        text = "hair styling"
        score = self.fuzzy_matcher.calculate_similarity(query, text)
        self.assertGreater(score, 0.4)  # Should match across languages


class TestSearchIndexer(TestCase):
    """Test search indexing functionality"""

    @staticmethod
    def random_choice(choices):
        """Get random choice from a list"""
        return random.choice(choices)

    def setUp(self):
        self.indexer = SearchIndexer()
        
        # Create test data
        self.category = ServiceCategoryFactory.create_category(name="Hair & Beauty")
        self.provider = ProviderFactory.create_provider(
            business_name="Elite Hair Salon",
            business_description="Professional hair styling services"
        )
        self.service = ServiceFactory.create_service(
            provider=self.provider,
            name="Hair Styling",
            description="Professional hair cutting and styling",
            category=self.category
        )
    
    def test_service_indexing_creates_searchable_fields(self):
        """Test that services are properly indexed with searchable fields"""
        indexed_service = self.indexer.index_service(self.service)
        
        self.assertIn('searchable_text', indexed_service)
        self.assertIn('hair styling', indexed_service['searchable_text'].lower())
        self.assertIn('elite hair salon', indexed_service['searchable_text'].lower())
        self.assertIn('hair & beauty', indexed_service['searchable_text'].lower())
    
    def test_search_index_includes_all_relevant_fields(self):
        """Test that search index includes all relevant searchable fields"""
        indexed_service = self.indexer.index_service(self.service)
        searchable_text = indexed_service['searchable_text'].lower()
        
        # Should include service name
        self.assertIn(self.service.name.lower(), searchable_text)
        # Should include service description
        self.assertIn(self.service.description.lower(), searchable_text)
        # Should include provider name
        self.assertIn(self.provider.business_name.lower(), searchable_text)
        # Should include category name
        self.assertIn(self.category.name.lower(), searchable_text)
    
    def test_index_performance_with_large_dataset(self):
        """Test indexing performance with large number of services"""
        # Create 100 test services
        # First create categories and providers
        categories = ServiceCategoryFactory.create_batch(5)
        providers = []
        for i in range(20):
            provider = ProviderFactory.create_provider()
            providers.append(provider)

        services = []
        for i in range(100):
            provider = self.random_choice(providers)
            service = ServiceFactory.create_service(provider=provider)
            services.append(service)
        
        import time
        start_time = time.time()
        
        indexed_services = []
        for service in services:
            indexed_services.append(self.indexer.index_service(service))
        
        end_time = time.time()
        indexing_time = end_time - start_time
        
        # Should index 100 services in less than 1 second
        self.assertLess(indexing_time, 1.0)
        self.assertEqual(len(indexed_services), 100)


class TestSearchResultRanker(TestCase):
    """Test search result ranking functionality"""
    
    def setUp(self):
        self.ranker = SearchResultRanker()
        
        # Create test services with different characteristics
        self.category = ServiceCategoryFactory.create_category(name="Hair & Beauty")
        self.provider1 = ProviderFactory.create_provider()
        self.provider2 = ProviderFactory.create_provider()
        self.provider3 = ProviderFactory.create_provider(is_featured=True)

        self.popular_service = ServiceFactory.create_service(
            provider=self.provider1,
            name="Popular Hair Salon",
            is_popular=True,
            booking_count=100
        )
        self.new_service = ServiceFactory.create_service(
            provider=self.provider2,
            name="New Hair Studio",
            is_popular=False,
            booking_count=5
        )
        self.featured_service = ServiceFactory.create_service(
            provider=self.provider3,
            name="Featured Beauty Spa",
            is_popular=True,
            booking_count=50
        )
    
    def test_exact_match_gets_highest_rank(self):
        """Test that exact matches receive highest ranking"""
        query = "Popular Hair Salon"
        services = [self.popular_service, self.new_service, self.featured_service]
        
        ranked_results = self.ranker.rank_results(query, services)
        
        # Popular service should be first due to exact match
        self.assertEqual(ranked_results[0]['service'].id, self.popular_service.id)
        self.assertGreater(ranked_results[0]['relevance_score'], 0.7)
    
    def test_popularity_affects_ranking(self):
        """Test that service popularity affects ranking"""
        query = "hair"  # Matches multiple services
        services = [self.new_service, self.popular_service]
        
        ranked_results = self.ranker.rank_results(query, services)
        
        # Popular service should rank higher
        self.assertEqual(ranked_results[0]['service'].id, self.popular_service.id)
    
    def test_featured_services_get_boost(self):
        """Test that featured services get ranking boost"""
        query = "beauty"
        services = [self.new_service, self.featured_service]
        
        ranked_results = self.ranker.rank_results(query, services)
        
        # Featured service should rank higher
        self.assertEqual(ranked_results[0]['service'].id, self.featured_service.id)
    
    def test_ranking_score_calculation(self):
        """Test that ranking scores are calculated correctly"""
        query = "hair"
        services = [self.popular_service]
        
        ranked_results = self.ranker.rank_results(query, services)
        result = ranked_results[0]
        
        # Should have all ranking components
        self.assertIn('relevance_score', result)
        self.assertIn('popularity_score', result)
        self.assertIn('final_score', result)
        
        # Scores should be between 0 and 1
        self.assertGreaterEqual(result['relevance_score'], 0.0)
        self.assertLessEqual(result['relevance_score'], 1.0)
        self.assertGreaterEqual(result['final_score'], 0.0)
        self.assertLessEqual(result['final_score'], 1.0)


class TestAdvancedSearchAlgorithm(TestCase):
    """Test the complete advanced search algorithm"""

    @staticmethod
    def random_choice(choices):
        """Get random choice from a list"""
        return random.choice(choices)

    def setUp(self):
        self.search_algorithm = AdvancedSearchAlgorithm()
        
        # Create comprehensive test data
        self.hair_category = ServiceCategoryFactory.create_category(name="Hair & Beauty")
        self.spa_category = ServiceCategoryFactory.create_category(name="Spa & Wellness")

        self.hair_provider = ProviderFactory.create_provider(
            business_name="Elite Hair Salon",
            city="Toronto"
        )
        self.spa_provider = ProviderFactory.create_provider(
            business_name="Zen Spa Retreat",
            city="Toronto"
        )

        self.hair_service = ServiceFactory.create_service(
            provider=self.hair_provider,
            name="Hair Styling and Cut",
            description="Professional hair cutting and styling services",
            category=self.hair_category,
            is_popular=True
        )
        self.spa_service = ServiceFactory.create_service(
            provider=self.spa_provider,
            name="Relaxing Massage",
            description="Full body relaxation massage therapy",
            category=self.spa_category,
            is_popular=True
        )
    
    def test_search_with_exact_match(self):
        """Test search with exact service name match"""
        results = self.search_algorithm.search("Hair Styling and Cut")

        self.assertGreater(len(results), 0)
        self.assertEqual(results[0]['service'].id, self.hair_service.id)
        self.assertGreater(results[0]['relevance_score'], 0.8)
    
    def test_search_with_typos(self):
        """Test search handles typos gracefully"""
        results = self.search_algorithm.search("Hair Stylingg")  # typo: extra 'g'

        self.assertGreater(len(results), 0)
        # Should still find the hair service despite typos
        found_hair_service = any(
            result['service'].id == self.hair_service.id
            for result in results
        )
        self.assertTrue(found_hair_service)

    def test_search_with_phonetic_queries(self):
        """Test search with phonetically similar queries"""
        # These tests should initially fail until phonetic search is implemented
        results = self.search_algorithm.search("masage")  # phonetic misspelling of massage

        self.assertGreater(len(results), 0)
        found_spa_service = any(
            result['service'].id == self.spa_service.id
            for result in results
        )
        self.assertTrue(found_spa_service)

    def test_search_with_keyboard_typos(self):
        """Test search handles keyboard layout typos"""
        # These tests should initially fail until keyboard-aware fuzzy matching is implemented
        results = self.search_algorithm.search("mair styling")  # 'n' -> 'm' keyboard typo

        self.assertGreater(len(results), 0)
        found_hair_service = any(
            result['service'].id == self.hair_service.id
            for result in results
        )
        self.assertTrue(found_hair_service)

    def test_search_with_abbreviations(self):
        """Test search with common abbreviations"""
        # These tests should initially fail until abbreviation expansion is implemented
        results = self.search_algorithm.search("apt booking")  # 'apt' for appointment

        # Should find services related to appointments/booking
        self.assertGreaterEqual(len(results), 0)  # May not have appointment services yet

    def test_search_with_contextual_synonyms(self):
        """Test search with contextual synonyms"""
        # These tests should initially fail until contextual synonym matching is implemented
        results = self.search_algorithm.search("trim")  # synonym for hair cut

        found_hair_service = any(
            result['service'].id == self.hair_service.id
            for result in results
        )
        self.assertTrue(found_hair_service)
    
    def test_search_with_partial_match(self):
        """Test search with partial matches"""
        results = self.search_algorithm.search("hair")
        
        self.assertGreater(len(results), 0)
        # Should find hair-related services
        found_hair_service = any(
            result['service'].id == self.hair_service.id 
            for result in results
        )
        self.assertTrue(found_hair_service)
    
    def test_search_by_provider_name(self):
        """Test search by provider business name"""
        results = self.search_algorithm.search("Elite Hair Salon")
        
        self.assertGreater(len(results), 0)
        self.assertEqual(results[0]['service'].provider.id, self.hair_provider.id)
    
    def test_search_by_category(self):
        """Test search by category name"""
        results = self.search_algorithm.search("Spa & Wellness")
        
        self.assertGreater(len(results), 0)
        found_spa_service = any(
            result['service'].id == self.spa_service.id 
            for result in results
        )
        self.assertTrue(found_spa_service)
    
    def test_empty_query_returns_empty_results(self):
        """Test that empty queries return empty results"""
        results = self.search_algorithm.search("")
        self.assertEqual(len(results), 0)
    
    def test_no_match_returns_empty_results(self):
        """Test that queries with no matches return empty results"""
        results = self.search_algorithm.search("nonexistent service xyz")
        self.assertEqual(len(results), 0)
    
    def test_search_performance_with_large_dataset(self):
        """Test search performance with large number of services"""
        # Create 200 additional services
        categories = ServiceCategoryFactory.create_batch(5)
        providers = []
        for i in range(40):
            provider = ProviderFactory.create_provider()
            providers.append(provider)

        for i in range(200):
            provider = self.random_choice(providers)
            ServiceFactory.create_service(provider=provider)
        
        import time
        start_time = time.time()
        
        results = self.search_algorithm.search("hair styling")
        
        end_time = time.time()
        search_time = end_time - start_time
        
        # Search should complete in less than 0.5 seconds
        self.assertLess(search_time, 0.5)
        self.assertGreaterEqual(len(results), 0)
    
    def test_search_result_structure(self):
        """Test that search results have correct structure"""
        results = self.search_algorithm.search("hair")

        if len(results) > 0:
            result = results[0]

            # Should have required fields
            self.assertIn('service', result)
            self.assertIn('relevance_score', result)
            self.assertIn('final_score', result)

            # Service should be a Service instance
            self.assertIsInstance(result['service'], Service)

            # Scores should be numeric
            self.assertIsInstance(result['relevance_score'], (int, float))
            self.assertIsInstance(result['final_score'], (int, float))

    def test_search_with_special_characters(self):
        """Test search handles special characters gracefully"""
        # These tests should initially fail until special character handling is enhanced
        results = self.search_algorithm.search("hair & beauty")
        self.assertGreaterEqual(len(results), 0)

        results = self.search_algorithm.search("nail's care")  # apostrophe
        self.assertGreaterEqual(len(results), 0)

        results = self.search_algorithm.search("spa/wellness")  # slash
        self.assertGreaterEqual(len(results), 0)

    def test_search_with_unicode_characters(self):
        """Test search with unicode and accented characters"""
        # These tests should initially fail until unicode normalization is implemented
        results = self.search_algorithm.search("beauté")  # French accented character
        self.assertGreaterEqual(len(results), 0)

        results = self.search_algorithm.search("niño")  # Spanish ñ character
        self.assertGreaterEqual(len(results), 0)

    def test_search_with_very_long_queries(self):
        """Test search performance with very long queries"""
        # These tests should initially fail until query optimization is implemented
        long_query = "hair styling and cutting with professional tools and expert beauticians for all hair types"
        results = self.search_algorithm.search(long_query)

        # Should handle long queries gracefully
        self.assertGreaterEqual(len(results), 0)

        # Should complete in reasonable time (tested in performance test)
        import time
        start_time = time.time()
        self.search_algorithm.search(long_query)
        end_time = time.time()
        self.assertLess(end_time - start_time, 1.0)  # Should complete in under 1 second


class TestSearchAlgorithmIntegration(TestCase):
    """Integration tests for the complete search system"""
    
    def setUp(self):
        # Create realistic test data
        self.beauty_category = ServiceCategoryFactory.create_category(name="Beauty & Cosmetics")
        self.wellness_category = ServiceCategoryFactory.create_category(name="Health & Wellness")

        # Create providers
        self.salon_provider = ProviderFactory.create_provider(
            business_name="Glamour Beauty Salon",
            business_description="Full service beauty salon",
            city="Toronto"
        )
        self.clinic_provider = ProviderFactory.create_provider(
            business_name="Wellness Health Clinic",
            business_description="Holistic health and wellness services",
            city="Toronto"
        )

        # Create services
        self.manicure_service = ServiceFactory.create_service(
            provider=self.salon_provider,
            name="Professional Manicure",
            description="Complete nail care and manicure service",
            category=self.beauty_category,
            base_price=45.00,
            is_popular=True
        )
        self.massage_service = ServiceFactory.create_service(
            provider=self.clinic_provider,
            name="Therapeutic Massage",
            description="Deep tissue therapeutic massage for wellness",
            category=self.wellness_category,
            base_price=80.00,
            is_popular=True
        )
    
    def test_end_to_end_search_workflow(self):
        """Test complete search workflow from query to ranked results"""
        search_algorithm = AdvancedSearchAlgorithm()
        
        # Test various search scenarios
        test_cases = [
            ("manicure", self.manicure_service),
            ("nail care", self.manicure_service),
            ("massage", self.massage_service),
            ("therapeutic", self.massage_service),
            ("Glamour", self.manicure_service),  # Provider name
            ("Beauty", self.manicure_service),   # Category name
        ]
        
        for query, expected_service in test_cases:
            with self.subTest(query=query):
                results = search_algorithm.search(query)
                
                self.assertGreater(len(results), 0, f"No results for query: {query}")
                
                # Check if expected service is in results
                found_service = any(
                    result['service'].id == expected_service.id 
                    for result in results
                )
                self.assertTrue(found_service, f"Expected service not found for query: {query}")
    
    def test_search_ranking_consistency(self):
        """Test that search ranking is consistent across multiple runs"""
        search_algorithm = AdvancedSearchAlgorithm()
        query = "beauty"
        
        # Run search multiple times
        results_1 = search_algorithm.search(query)
        results_2 = search_algorithm.search(query)
        results_3 = search_algorithm.search(query)
        
        # Results should be consistent
        self.assertEqual(len(results_1), len(results_2))
        self.assertEqual(len(results_2), len(results_3))
        
        if len(results_1) > 0:
            # Top result should be the same
            self.assertEqual(
                results_1[0]['service'].id,
                results_2[0]['service'].id
            )
            self.assertEqual(
                results_2[0]['service'].id,
                results_3[0]['service'].id
            )


class TestRealTimeSearchSuggestions(TestCase):
    """Test real-time search suggestions functionality"""

    def setUp(self):
        """Set up test data for search suggestions"""
        from catalog.search_algorithms import SearchSuggestionEngine
        self.suggestion_engine = SearchSuggestionEngine()

        # Create test categories
        self.hair_category = ServiceCategoryFactory.create_category(
            name="Hair & Beauty",
            slug="hair-beauty"
        )
        self.spa_category = ServiceCategoryFactory.create_category(
            name="Spa & Wellness",
            slug="spa-wellness"
        )

        # Create test providers
        self.hair_provider = ProviderFactory.create_provider(
            business_name="Elite Hair Studio",
            category_slug="hair-beauty"
        )
        self.spa_provider = ProviderFactory.create_provider(
            business_name="Zen Spa Retreat",
            category_slug="spa-wellness"
        )

        # Create test services
        self.hair_service = ServiceFactory.create_service(
            provider=self.hair_provider,
            name="Hair Styling and Cut",
            description="Professional hair styling and cutting services",
            category=self.hair_category
        )
        self.massage_service = ServiceFactory.create_service(
            provider=self.spa_provider,
            name="Relaxing Massage Therapy",
            description="Full body relaxation massage",
            category=self.spa_category
        )

    def test_basic_search_suggestions(self):
        """Test basic search suggestion generation"""
        suggestions = self.suggestion_engine.get_suggestions("hai")

        self.assertGreater(len(suggestions), 0)
        # Should suggest "hair" related terms
        suggestion_text = " ".join(suggestions).lower()
        self.assertIn("hair", suggestion_text)

    def test_suggestion_ranking_by_popularity(self):
        """Test that suggestions are ranked by popularity/relevance"""
        suggestions = self.suggestion_engine.get_suggestions("h")

        self.assertGreater(len(suggestions), 0)
        # First suggestion should be most relevant
        self.assertTrue(suggestions[0].startswith("h") or "h" in suggestions[0].lower())

    def test_suggestion_debouncing_performance(self):
        """Test that suggestion generation is fast enough for real-time use"""
        import time

        start_time = time.time()
        suggestions = self.suggestion_engine.get_suggestions("beauty")
        end_time = time.time()

        # Should complete in less than 100ms for real-time feel
        self.assertLess(end_time - start_time, 0.1)
        self.assertGreater(len(suggestions), 0)

    def test_suggestion_caching(self):
        """Test that suggestions are cached for performance"""
        # Clear cache first
        self.suggestion_engine.cache.clear()

        # First call - should populate cache
        suggestions1 = self.suggestion_engine.get_suggestions("massage")
        cache_key = "massage_5_None"
        self.assertIn(cache_key, self.suggestion_engine.cache)

        # Second call - should use cache
        suggestions2 = self.suggestion_engine.get_suggestions("massage")

        # Results should be identical
        self.assertEqual(suggestions1, suggestions2)
        # Cache should still contain the entry
        self.assertIn(cache_key, self.suggestion_engine.cache)

    def test_suggestion_limit(self):
        """Test that suggestions respect the limit parameter"""
        suggestions = self.suggestion_engine.get_suggestions("s", limit=3)
        self.assertLessEqual(len(suggestions), 3)

    def test_empty_query_suggestions(self):
        """Test handling of empty or very short queries"""
        # Empty query
        suggestions = self.suggestion_engine.get_suggestions("")
        self.assertEqual(len(suggestions), 0)

        # Single character (should return popular terms)
        suggestions = self.suggestion_engine.get_suggestions("h")
        self.assertGreaterEqual(len(suggestions), 0)

    def test_suggestion_context_awareness(self):
        """Test that suggestions are context-aware"""
        # Hair-related context
        hair_suggestions = self.suggestion_engine.get_suggestions("cut", context="hair")

        # Should include hair-related suggestions
        suggestion_text = " ".join(hair_suggestions).lower()
        self.assertTrue(
            any(term in suggestion_text for term in ["hair", "styling", "trim"])
        )


class TestVoiceSearchIntegration(TestCase):
    """Test voice search integration capabilities"""

    def setUp(self):
        """Set up test data for voice search"""
        from catalog.search_algorithms import VoiceSearchProcessor
        self.voice_processor = VoiceSearchProcessor()

        # Create test services for voice search
        self.hair_category = ServiceCategoryFactory.create_category(
            name="Hair & Beauty",
            slug="hair-beauty"
        )

        self.hair_provider = ProviderFactory.create_provider(
            business_name="Voice Hair Studio",
            category_slug="hair-beauty"
        )

        self.hair_service = ServiceFactory.create_service(
            provider=self.hair_provider,
            name="Hair Cut and Styling",
            description="Professional hair cutting and styling services",
            category=self.hair_category
        )

    def test_voice_query_normalization(self):
        """Test that voice queries are properly normalized"""
        # Simulate voice input with common speech patterns
        voice_queries = [
            "I need a haircut please",
            "Find me a hair salon nearby",
            "Book a massage appointment",
            "Show me beauty services"
        ]

        for voice_query in voice_queries:
            normalized = self.voice_processor.normalize_voice_query(voice_query)

            # Should remove filler words and extract key terms
            self.assertNotIn("please", normalized.lower())
            self.assertNotIn("find me", normalized.lower())
            self.assertNotIn("show me", normalized.lower())

            # Should contain relevant search terms
            if "haircut" in voice_query or "hair salon" in voice_query:
                self.assertIn("hair", normalized.lower())

    def test_voice_command_processing(self):
        """Test processing of voice commands"""
        commands = [
            ("book hair appointment", {"action": "book", "service_type": "hair"}),
            ("find nearby spa", {"action": "search", "service_type": "spa", "filter": "nearby"}),
            ("show me massage prices", {"action": "search", "service_type": "massage", "info": "prices"}),
        ]

        for voice_input, expected_intent in commands:
            intent = self.voice_processor.extract_intent(voice_input)

            self.assertEqual(intent["action"], expected_intent["action"])
            self.assertEqual(intent["service_type"], expected_intent["service_type"])

    def test_voice_search_accuracy(self):
        """Test that voice search produces accurate results"""
        voice_query = "I want to get my hair cut"

        # Process voice query
        search_query = self.voice_processor.voice_to_search_query(voice_query)

        # Should extract "hair cut" as the main search term
        self.assertIn("hair", search_query.lower())
        self.assertTrue(
            any(term in search_query.lower() for term in ["cut", "cutting", "style"])
        )

    def test_voice_feedback_generation(self):
        """Test generation of voice search feedback"""
        search_results = [
            {"service": self.hair_service, "relevance_score": 0.95}
        ]

        feedback = self.voice_processor.generate_voice_feedback(search_results)

        # Should provide natural language feedback
        self.assertIsInstance(feedback, str)
        self.assertGreater(len(feedback), 0)
        self.assertIn("found", feedback.lower())

    def test_voice_error_handling(self):
        """Test handling of unclear or invalid voice input"""
        unclear_inputs = [
            "",  # Empty input
            "umm... uh... I want...",  # Filler words only
            "asdfghjkl",  # Gibberish
        ]

        for unclear_input in unclear_inputs:
            result = self.voice_processor.process_voice_input(unclear_input)

            # Should handle gracefully
            self.assertIsNotNone(result)
            self.assertIn("error", result.get("status", "").lower())

    def test_voice_search_performance(self):
        """Test that voice processing is fast enough for real-time use"""
        import time

        voice_input = "Find me a good hair salon"

        start_time = time.time()
        result = self.voice_processor.process_voice_input(voice_input)
        end_time = time.time()

        # Should process in less than 200ms
        self.assertLess(end_time - start_time, 0.2)
        self.assertIsNotNone(result)
