/**
 * SmartCache Component Tests
 * Tests for intelligent caching system with TTL, LRU eviction, and analytics
 * Following TDD protocol - these tests will initially fail
 */

import React from 'react';
import { render, waitFor, act } from '@testing-library/react-native';
import { Text, View } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  SmartCache,
  useSmartCache,
  SmartCacheProvider,
  CacheAnalytics,
} from '../utils/SmartCache';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  getAllKeys: jest.fn(),
  multiGet: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
  clear: jest.fn(),
}));

// Mock Date for consistent testing
const mockDate = new Date('2025-08-08T14:00:00Z');
jest.spyOn(global, 'Date').mockImplementation(() => mockDate);

describe('SmartCache System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
    (AsyncStorage.removeItem as jest.Mock).mockResolvedValue(undefined);
    (AsyncStorage.getAllKeys as jest.Mock).mockResolvedValue([]);
    (AsyncStorage.multiGet as jest.Mock).mockResolvedValue([]);
    (AsyncStorage.multiSet as jest.Mock).mockResolvedValue(undefined);
    (AsyncStorage.multiRemove as jest.Mock).mockResolvedValue(undefined);
    (AsyncStorage.clear as jest.Mock).mockResolvedValue(undefined);
  });

  describe('SmartCache Core Functionality', () => {
    let cache: SmartCache;

    beforeEach(() => {
      cache = new SmartCache({
        maxSize: 100,
        defaultTTL: 300000, // 5 minutes
        enableAnalytics: true,
        enablePersistence: true,
      });
    });

    it('should initialize with default configuration', () => {
      const defaultCache = new SmartCache();
      expect(defaultCache).toBeDefined();
      expect(defaultCache.getStats().maxSize).toBe(50); // Default max size
    });

    it('should set and get cache entries', async () => {
      const testData = { id: 1, name: 'Test Item' };
      
      await cache.set('test-key', testData);
      const result = await cache.get('test-key');
      
      expect(result).toEqual(testData);
    });

    it('should handle cache misses', async () => {
      const result = await cache.get('non-existent-key');
      expect(result).toBeNull();
    });

    it('should respect TTL expiration', async () => {
      const testData = { id: 1, name: 'Test Item' };
      
      // Set with 1ms TTL
      await cache.set('test-key', testData, { ttl: 1 });
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const result = await cache.get('test-key');
      expect(result).toBeNull();
    });

    it('should implement LRU eviction', async () => {
      // Create cache with size limit of 2
      const smallCache = new SmartCache({ maxSize: 2 });
      
      await smallCache.set('key1', 'value1');
      await smallCache.set('key2', 'value2');
      await smallCache.set('key3', 'value3'); // Should evict key1
      
      expect(await smallCache.get('key1')).toBeNull();
      expect(await smallCache.get('key2')).toBe('value2');
      expect(await smallCache.get('key3')).toBe('value3');
    });

    it('should update LRU order on access', async () => {
      const smallCache = new SmartCache({ maxSize: 2 });
      
      await smallCache.set('key1', 'value1');
      await smallCache.set('key2', 'value2');
      
      // Access key1 to make it most recently used
      await smallCache.get('key1');
      
      // Add key3, should evict key2 (least recently used)
      await smallCache.set('key3', 'value3');
      
      expect(await smallCache.get('key1')).toBe('value1');
      expect(await smallCache.get('key2')).toBeNull();
      expect(await smallCache.get('key3')).toBe('value3');
    });

    it('should support cache invalidation', async () => {
      await cache.set('test-key', 'test-value');
      expect(await cache.get('test-key')).toBe('test-value');
      
      await cache.invalidate('test-key');
      expect(await cache.get('test-key')).toBeNull();
    });

    it('should support pattern-based invalidation', async () => {
      await cache.set('user:1:profile', { id: 1, name: 'User 1' });
      await cache.set('user:2:profile', { id: 2, name: 'User 2' });
      await cache.set('post:1:data', { id: 1, title: 'Post 1' });
      
      await cache.invalidatePattern('user:*');
      
      expect(await cache.get('user:1:profile')).toBeNull();
      expect(await cache.get('user:2:profile')).toBeNull();
      expect(await cache.get('post:1:data')).toEqual({ id: 1, title: 'Post 1' });
    });

    it('should clear all cache entries', async () => {
      await cache.set('key1', 'value1');
      await cache.set('key2', 'value2');
      
      await cache.clear();
      
      expect(await cache.get('key1')).toBeNull();
      expect(await cache.get('key2')).toBeNull();
    });
  });

  describe('Cache Analytics', () => {
    let cache: SmartCache;

    beforeEach(() => {
      cache = new SmartCache({
        maxSize: 100,
        enableAnalytics: true,
      });
    });

    it('should track cache hits and misses', async () => {
      await cache.set('test-key', 'test-value');
      
      // Hit
      await cache.get('test-key');
      
      // Miss
      await cache.get('non-existent-key');
      
      const stats = cache.getStats();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe(0.5);
    });

    it('should track cache operations', async () => {
      await cache.set('key1', 'value1');
      await cache.set('key2', 'value2');
      await cache.invalidate('key1');
      
      const stats = cache.getStats();
      expect(stats.sets).toBe(2);
      expect(stats.invalidations).toBe(1);
    });

    it('should track memory usage', async () => {
      const largeData = { data: 'x'.repeat(1000) };
      await cache.set('large-key', largeData);
      
      const stats = cache.getStats();
      expect(stats.memoryUsage).toBeGreaterThan(0);
      expect(stats.size).toBe(1);
    });

    it('should provide cache analytics report', async () => {
      await cache.set('key1', 'value1');
      await cache.get('key1'); // Hit
      await cache.get('key2'); // Miss
      
      const analytics = cache.getAnalytics();
      expect(analytics).toEqual({
        hitRate: 0.5,
        missRate: 0.5,
        totalOperations: 2,
        averageResponseTime: expect.any(Number),
        topKeys: expect.any(Array),
        evictionRate: 0,
        memoryEfficiency: expect.any(Number),
      });
    });
  });

  describe('Persistence', () => {
    let cache: SmartCache;

    beforeEach(() => {
      cache = new SmartCache({
        enablePersistence: true,
        persistenceKey: 'test-cache',
      });
    });

    it('should persist cache to AsyncStorage', async () => {
      await cache.set('test-key', 'test-value');
      
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'smart-cache:test-cache',
        expect.stringContaining('test-key')
      );
    });

    it('should restore cache from AsyncStorage', async () => {
      const persistedData = JSON.stringify({
        'test-key': {
          value: 'test-value',
          timestamp: Date.now(),
          ttl: 300000,
          accessCount: 1,
        }
      });
      
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(persistedData);
      
      const newCache = new SmartCache({
        enablePersistence: true,
        persistenceKey: 'test-cache',
      });
      
      await newCache.restore();
      
      expect(await newCache.get('test-key')).toBe('test-value');
    });

    it('should handle corrupted persistence data', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('invalid-json');
      
      const newCache = new SmartCache({
        enablePersistence: true,
        persistenceKey: 'test-cache',
      });
      
      // Should not throw error
      await expect(newCache.restore()).resolves.not.toThrow();
    });
  });

  describe('useSmartCache Hook', () => {
    const TestComponent = ({ cacheKey, data }: any) => {
      const { 
        data: cachedData, 
        isLoading, 
        error, 
        set, 
        invalidate,
        refresh 
      } = useSmartCache(cacheKey, {
        fetcher: async () => data,
        ttl: 60000,
      });

      return (
        <View testID="cache-component">
          <Text testID="loading">{isLoading ? 'Loading' : 'Loaded'}</Text>
          <Text testID="data">{JSON.stringify(cachedData)}</Text>
          <Text testID="error">{error ? error.message : 'No Error'}</Text>
        </View>
      );
    };

    it('should provide cached data', async () => {
      const testData = { id: 1, name: 'Test' };
      
      const { getByTestId } = render(
        <SmartCacheProvider>
          <TestComponent cacheKey="test-key" data={testData} />
        </SmartCacheProvider>
      );

      await waitFor(() => {
        expect(getByTestId('loading').children[0]).toBe('Loaded');
        expect(getByTestId('data').children[0]).toBe(JSON.stringify(testData));
      });
    });

    it('should handle loading states', async () => {
      const { getByTestId } = render(
        <SmartCacheProvider>
          <TestComponent cacheKey="test-key" data={{ id: 1 }} />
        </SmartCacheProvider>
      );

      // Should initially show loading
      expect(getByTestId('loading').children[0]).toBe('Loading');
    });

    it('should handle errors gracefully', async () => {
      const TestErrorComponent = () => {
        const { error } = useSmartCache('error-key', {
          fetcher: async () => {
            throw new Error('Test error');
          },
        });

        return <Text testID="error">{error ? error.message : 'No Error'}</Text>;
      };

      const { getByTestId } = render(
        <SmartCacheProvider>
          <TestErrorComponent />
        </SmartCacheProvider>
      );

      await waitFor(() => {
        expect(getByTestId('error').children[0]).toBe('Test error');
      });
    });

    it('should support manual cache operations', async () => {
      const TestManualComponent = () => {
        const { set, invalidate, data } = useSmartCache('manual-key');

        React.useEffect(() => {
          set({ manual: true });
        }, [set]);

        return <Text testID="manual-data">{JSON.stringify(data)}</Text>;
      };

      const { getByTestId } = render(
        <SmartCacheProvider>
          <TestManualComponent />
        </SmartCacheProvider>
      );

      await waitFor(() => {
        expect(getByTestId('manual-data').children[0]).toBe('{"manual":true}');
      });
    });
  });

  describe('SmartCacheProvider', () => {
    it('should provide cache context to children', () => {
      const TestChild = () => {
        const { cache } = useSmartCache('test');
        return <Text testID="cache-available">{cache ? 'Available' : 'Not Available'}</Text>;
      };

      const { getByTestId } = render(
        <SmartCacheProvider>
          <TestChild />
        </SmartCacheProvider>
      );

      expect(getByTestId('cache-available').children[0]).toBe('Available');
    });

    it('should support custom cache configuration', () => {
      const customConfig = {
        maxSize: 200,
        defaultTTL: 600000,
        enableAnalytics: false,
      };

      const TestChild = () => {
        const { cache } = useSmartCache('test');
        const stats = cache?.getStats();
        return <Text testID="max-size">{stats?.maxSize}</Text>;
      };

      const { getByTestId } = render(
        <SmartCacheProvider config={customConfig}>
          <TestChild />
        </SmartCacheProvider>
      );

      expect(getByTestId('max-size').children[0]).toBe('200');
    });
  });

  describe('Advanced Features', () => {
    let cache: SmartCache;

    beforeEach(() => {
      cache = new SmartCache({
        maxSize: 100,
        enableAnalytics: true,
      });
    });

    it('should support cache warming', async () => {
      const warmupData = {
        'key1': 'value1',
        'key2': 'value2',
        'key3': 'value3',
      };

      await cache.warmup(warmupData);

      expect(await cache.get('key1')).toBe('value1');
      expect(await cache.get('key2')).toBe('value2');
      expect(await cache.get('key3')).toBe('value3');
    });

    it('should support batch operations', async () => {
      const batchData = [
        { key: 'batch1', value: 'value1' },
        { key: 'batch2', value: 'value2' },
        { key: 'batch3', value: 'value3' },
      ];

      await cache.setBatch(batchData);

      const results = await cache.getBatch(['batch1', 'batch2', 'batch3']);
      expect(results).toEqual({
        batch1: 'value1',
        batch2: 'value2',
        batch3: 'value3',
      });
    });

    it('should support cache compression', async () => {
      const largeData = { data: 'x'.repeat(10000) };
      
      await cache.set('large-key', largeData, { compress: true });
      const result = await cache.get('large-key');
      
      expect(result).toEqual(largeData);
    });

    it('should support cache tags for group invalidation', async () => {
      await cache.set('user:1', { id: 1 }, { tags: ['user', 'profile'] });
      await cache.set('user:2', { id: 2 }, { tags: ['user', 'profile'] });
      await cache.set('post:1', { id: 1 }, { tags: ['post'] });

      await cache.invalidateByTag('user');

      expect(await cache.get('user:1')).toBeNull();
      expect(await cache.get('user:2')).toBeNull();
      expect(await cache.get('post:1')).toEqual({ id: 1 });
    });

    it('should support cache dependencies', async () => {
      await cache.set('parent', { id: 1 });
      await cache.set('child', { parentId: 1 }, { dependencies: ['parent'] });

      await cache.invalidate('parent');

      expect(await cache.get('parent')).toBeNull();
      expect(await cache.get('child')).toBeNull(); // Should be invalidated due to dependency
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle memory pressure', async () => {
      const cache = new SmartCache({
        maxSize: 10,
        enableMemoryPressureHandling: true,
      });

      // Fill cache beyond capacity
      for (let i = 0; i < 20; i++) {
        await cache.set(`key${i}`, `value${i}`);
      }

      const stats = cache.getStats();
      expect(stats.size).toBeLessThanOrEqual(10);
    });

    it('should optimize memory usage', async () => {
      const cache = new SmartCache({
        maxSize: 100,
        enableMemoryOptimization: true,
      });

      // Add data with different access patterns
      await cache.set('frequent', 'value', { priority: 'high' });
      await cache.set('infrequent', 'value', { priority: 'low' });

      // Simulate memory pressure
      await cache.optimizeMemory();

      const stats = cache.getStats();
      expect(stats.memoryUsage).toBeGreaterThan(0);
    });
  });
});
