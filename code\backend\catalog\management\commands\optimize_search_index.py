"""
Django management command to optimize the search index
"""

from django.core.management.base import BaseCommand
from catalog.search_algorithms import SearchIndexManager


class Command(BaseCommand):
    help = 'Optimize the search index for better performance'

    def handle(self, *args, **options):
        self.stdout.write('Starting search index optimization...')

        index_manager = SearchIndexManager()

        if not index_manager.index_exists():
            self.stdout.write(
                self.style.ERROR('No search index found. Run rebuild_search_index first.')
            )
            return

        result = index_manager.optimize_index()

        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully optimized search index. '
                    f'Performance improvement: {result["performance_improvement"]} entries optimized.'
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR(
                    f'Failed to optimize search index: {result.get("error", "Unknown error")}'
                )
            )