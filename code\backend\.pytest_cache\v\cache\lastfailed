{"authentication/tests.py": true, "debug_test.py::test_registration": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_all_accounts_database_consistency": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_email_not_verified_accounts": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_invalid_credentials_accounts": true, "test_consolidated_accounts.py::TestAccountPasswordValidationTest::test_password_strength_requirements": true, "test_consolidated_accounts.py::TestAccountAPIEndpointsTest::test_login_endpoint_with_all_accounts": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_database_settings_configuration": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_django_database_connection": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_postgresql_connection_available": true, "test_postgresql_config.py::PostgreSQLMigrationTest::test_authentication_tables_exist": true, "test_postgresql_config.py::PostgreSQLMigrationTest::test_catalog_tables_exist": true, "test_postgresql_config.py::PostgreSQLDataIntegrityTest::test_foreign_key_relationships": true, "test_postgresql_config.py::PostgreSQLPerformanceTest::test_database_indexes_exist": true, "test_postgresql_config.py::PostgreSQLEnvironmentTest::test_postgresql_version_compatibility": true, "test_audit_database_config.py::DatabaseConfigurationAuditTest::test_database_settings_structure_matches_reference": true, "test_audit_database_config.py::DatabaseConfigurationAuditTest::test_postgresql_configuration_matches_reference": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_environment_based_settings_structure_exists": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_environment_variable_configuration": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_postgresql_configuration_structure_when_available": true, "test_postgresql_config.py::PostgreSQLEnvironmentTest::test_sqlite_fallback_disabled": true, "test_api_architecture_restructuring.py::APIArchitectureRestructuringTest::test_api_documentation_updated": true, "test_api_architecture_restructuring.py::APIArchitectureRestructuringTest::test_customer_api_endpoints": true, "test_api_architecture_restructuring.py::APIArchitectureRestructuringTest::test_provider_api_endpoints": true, "test_connection_pooling_health_checks.py::ConnectionPoolingHealthChecksTest::test_connection_health_check_on_reuse": true, "test_database_configuration_analysis.py::DatabaseConfigurationAnalysisTest::test_current_database_fallback_behavior": true, "test_environment_configuration.py::EnvironmentConfigurationTest::test_database_configuration_with_environment_variables": true, "test_environment_configuration.py::EnvironmentConfigurationTest::test_environment_variable_precedence": true, "test_environment_configuration.py::EnvironmentConfigurationTest::test_missing_environment_variables_fallback": true, "test_environment_configuration.py::EnvironmentConfigurationTest::test_postgresql_connection_with_correct_environment": true, "test_environment_configuration.py::EnvironmentConfigurationTest::test_sqlite_fallback_behavior": true, "test_jwt_rs256_implementation.py::test_jwt_token_verification": true, "test_jwt_rs256_implementation.py::test_jwt_token_refresh": true, "test_postgresql_connection_settings.py::PostgreSQLConnectionSettingsTest::test_fallback_behavior_with_connection_failure": true, "test_postgresql_connection_settings.py::PostgreSQLConnectionSettingsTest::test_postgresql_connection_parameters": true, "test_postgresql_connection_settings.py::PostgreSQLConnectionSettingsTest::test_ssl_configuration_options": true, "test_postgresql_setup.py::PostgreSQLSetupTest::test_create_vierla_database_and_user": true, "test_postgresql_setup.py::PostgreSQLSetupTest::test_django_postgresql_connection": true, "test_postgresql_setup.py::PostgreSQLSetupTest::test_postgresql_configuration_optimization": true, "test_postgresql_setup.py::PostgreSQLSetupTest::test_postgresql_service_accessibility": true, "test_postgresql_setup.py::PostgreSQLSetupTest::test_vierla_user_connection": true, "catalog/tests/test_advanced_search_algorithm.py::TestAdvancedSearchAlgorithm::test_search_with_contextual_synonyms": true, "catalog/tests/test_advanced_search_algorithm.py::TestVoiceSearchIntegration::test_voice_error_handling": true, "catalog/tests/test_advanced_search_algorithm.py::TestVoiceSearchIntegration::test_voice_search_accuracy": true, "catalog/tests/test_advanced_search_algorithm.py::TestAdvancedSearchAlgorithm::test_no_match_returns_empty_results": true, "catalog/tests/test_advanced_search_algorithm.py::TestVoiceSearchIntegration::test_voice_query_normalization": true, "catalog/tests/test_search_indexing.py::TestSearchIndexingSystem::test_index_optimization": true, "catalog/tests/test_search_indexing.py::TestSearchIndexingSystem::test_index_update_on_service_modification": true, "catalog/tests/test_realtime_search_suggestions.py::TestCase": true, "catalog/tests/test_realtime_search_suggestions.py::TestRealtimeSearchSuggestions": true, "catalog/tests/test_realtime_search_suggestions.py::TestSuggestionTypes": true, "catalog/tests/test_realtime_search_suggestions.py::TestSuggestionCaching": true, "catalog/tests/test_realtime_search_suggestions.py::TestSuggestionIntegration": true, "catalog/tests/test_realtime_search_suggestions.py::TestRealtimeSearchSuggestions::test_fuzzy_matching_in_suggestions": true, "catalog/tests/test_realtime_search_suggestions.py::TestSuggestionIntegration::test_end_to_end_suggestion_workflow": true, "catalog/tests/test_realtime_search_suggestions.py::TestSuggestionIntegration::test_suggestion_consistency_with_search": true, "catalog/tests/test_realtime_search_suggestions.py::TestSuggestionIntegration::test_suggestion_to_search_integration": true, "catalog/tests/test_search_indexing.py::TestSearchIndexingSystem::test_index_update_on_service_deletion": true}