"""
Django signals for automatic search index updates
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import Service
from .search_algorithms import SearchIndexManager


@receiver(post_save, sender=Service)
def update_search_index_on_service_save(sender, instance, created, **kwargs):
    """Update search index when a service is created or updated"""
    if instance.is_active:
        index_manager = SearchIndexManager()
        index_manager.update_service_index(instance)


@receiver(post_delete, sender=Service)
def update_search_index_on_service_delete(sender, instance, **kwargs):
    """Update search index when a service is deleted"""
    index_manager = SearchIndexManager()
    index_manager.remove_service_from_index(instance.id)