/**
 * LazyFlatList Component
 * Performance-optimized FlatList with lazy loading, virtualization, and advanced features
 * Implements progressive loading, memory management, search, filtering, and accessibility
 */

import React, { 
  useState, 
  useEffect, 
  useRef, 
  useCallback, 
  useMemo,
} from 'react';
import {
  FlatList,
  View,
  Text,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
  ListRenderItem,
  ViewToken,
  AccessibilityInfo,
  FlatListProps,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface LazyFlatListProps<T> extends Omit<FlatListProps<T>, 'data' | 'renderItem'> {
  data: T[];
  renderItem: ListRenderItem<T>;
  
  // Performance options
  initialBatchSize?: number;
  batchSize?: number;
  enableVirtualization?: boolean;
  windowSize?: number;
  maxToRenderPerBatch?: number;
  enableMemoryManagement?: boolean;
  memoryThreshold?: number;
  enableItemRecycling?: boolean;
  recycleThreshold?: number;
  enableProgressiveLoading?: boolean;
  progressiveLoadThreshold?: number;
  
  // Loading states
  loading?: boolean;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  error?: Error | null;
  errorComponent?: React.ReactNode;
  showSkeleton?: boolean;
  skeletonComponent?: React.ReactNode;
  skeletonCount?: number;
  
  // Search and filtering
  searchable?: boolean;
  searchQuery?: string;
  onSearch?: (query: string) => void;
  filterable?: boolean;
  filterFunction?: (item: T) => boolean;
  sortable?: boolean;
  sortFunction?: (a: T, b: T) => number;
  
  // Refresh
  enablePullToRefresh?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
  
  // Accessibility
  announceLoadingStates?: boolean;
  onAccessibilityAnnouncement?: (message: string) => void;
  enableKeyboardNavigation?: boolean;
  
  // Performance tracking
  trackScrollPerformance?: boolean;
  trackRenderPerformance?: boolean;
  onScrollMetrics?: (metrics: ScrollMetrics) => void;
  onRenderMetrics?: (metrics: RenderMetrics) => void;
  onMemoryWarning?: (info: MemoryInfo) => void;
  onProgressiveLoad?: () => void;
  
  // Callbacks
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
}

interface ScrollMetrics {
  scrollOffset: number;
  scrollVelocity: number;
  fps: number;
}

interface RenderMetrics {
  renderTime: number;
  itemCount: number;
  memoryUsage: number;
}

interface MemoryInfo {
  itemCount: number;
  memoryUsage: number;
}

export function LazyFlatList<T>({
  data,
  renderItem,
  keyExtractor,
  initialBatchSize = 10,
  batchSize = 10,
  enableVirtualization = true,
  windowSize = 10,
  maxToRenderPerBatch = 5,
  enableMemoryManagement = false,
  memoryThreshold = 0.8,
  enableItemRecycling = false,
  recycleThreshold = 50,
  enableProgressiveLoading = false,
  progressiveLoadThreshold = 0.7,
  loading = false,
  loadingComponent,
  emptyComponent,
  error = null,
  errorComponent,
  showSkeleton = false,
  skeletonComponent,
  skeletonCount = 5,
  searchable = false,
  searchQuery = '',
  onSearch,
  filterable = false,
  filterFunction,
  sortable = false,
  sortFunction,
  enablePullToRefresh = false,
  refreshing = false,
  onRefresh,
  announceLoadingStates = false,
  onAccessibilityAnnouncement,
  enableKeyboardNavigation = false,
  trackScrollPerformance = false,
  trackRenderPerformance = false,
  onScrollMetrics,
  onRenderMetrics,
  onMemoryWarning,
  onProgressiveLoad,
  onEndReached,
  onEndReachedThreshold = 0.5,
  testID,
  ...flatListProps
}: LazyFlatListProps<T>) {
  const theme = useTheme();
  const [visibleData, setVisibleData] = useState<T[]>([]);
  const [currentBatch, setCurrentBatch] = useState(0);
  const [renderStartTime, setRenderStartTime] = useState<number>(0);
  
  const flatListRef = useRef<FlatList<T>>(null);
  const scrollMetricsRef = useRef({ offset: 0, velocity: 0, fps: 60 });

  // Process data (search, filter, sort)
  const processedData = useMemo(() => {
    let result = [...data];

    // Apply search
    if (searchable && searchQuery && onSearch) {
      onSearch(searchQuery);
      // In real implementation, would filter based on search query
    }

    // Apply filter
    if (filterable && filterFunction) {
      result = result.filter(filterFunction);
    }

    // Apply sort
    if (sortable && sortFunction) {
      result = result.sort(sortFunction);
    }

    return result;
  }, [data, searchable, searchQuery, onSearch, filterable, filterFunction, sortable, sortFunction]);

  // Update visible data based on current batch
  useEffect(() => {
    const endIndex = (currentBatch + 1) * batchSize;
    const newVisibleData = processedData.slice(0, Math.min(endIndex, processedData.length));
    setVisibleData(newVisibleData);
  }, [processedData, currentBatch, batchSize]);

  // Initialize with first batch
  useEffect(() => {
    if (processedData.length > 0) {
      const initialData = processedData.slice(0, initialBatchSize);
      setVisibleData(initialData);
      setCurrentBatch(0);
    }
  }, [processedData, initialBatchSize]);

  // Memory management
  useEffect(() => {
    if (enableMemoryManagement) {
      const memoryUsage = visibleData.length * 0.1; // Simulate memory calculation
      
      if (memoryUsage > memoryThreshold * 100) {
        onMemoryWarning?.({
          itemCount: visibleData.length,
          memoryUsage,
        });
      }
    }
  }, [enableMemoryManagement, visibleData.length, memoryThreshold, onMemoryWarning]);

  // Progressive loading
  useEffect(() => {
    if (enableProgressiveLoading && onProgressiveLoad) {
      const loadedRatio = visibleData.length / processedData.length;
      if (loadedRatio >= progressiveLoadThreshold) {
        onProgressiveLoad();
      }
    }
  }, [enableProgressiveLoading, visibleData.length, processedData.length, progressiveLoadThreshold, onProgressiveLoad]);

  // Render performance tracking
  useEffect(() => {
    if (trackRenderPerformance) {
      setRenderStartTime(Date.now());
    }
  }, [trackRenderPerformance, visibleData]);

  useEffect(() => {
    if (trackRenderPerformance && renderStartTime > 0 && onRenderMetrics) {
      const renderTime = Date.now() - renderStartTime;
      onRenderMetrics({
        renderTime,
        itemCount: visibleData.length,
        memoryUsage: visibleData.length * 0.1,
      });
    }
  }, [trackRenderPerformance, renderStartTime, visibleData.length, onRenderMetrics]);

  // Handle end reached
  const handleEndReached = useCallback(() => {
    if (visibleData.length < processedData.length) {
      setCurrentBatch(prev => prev + 1);
    }
    onEndReached?.();
  }, [visibleData.length, processedData.length, onEndReached]);

  // Handle scroll
  const handleScroll = useCallback((event: any) => {
    const { contentOffset, velocity } = event.nativeEvent;
    scrollMetricsRef.current = {
      offset: contentOffset.y,
      velocity: velocity?.y || 0,
      fps: 60, // Would calculate actual FPS in real implementation
    };

    if (trackScrollPerformance && onScrollMetrics) {
      onScrollMetrics({
        scrollOffset: contentOffset.y,
        scrollVelocity: velocity?.y || 0,
        fps: 60,
      });
    }
  }, [trackScrollPerformance, onScrollMetrics]);

  // Handle viewable items changed
  const handleViewableItemsChanged = useCallback(({ viewableItems }: { viewableItems: ViewToken[] }) => {
    // Handle item recycling
    if (enableItemRecycling && viewableItems.length > recycleThreshold) {
      // In real implementation, would recycle off-screen items
    }
  }, [enableItemRecycling, recycleThreshold]);

  // Accessibility announcements
  useEffect(() => {
    if (announceLoadingStates && loading) {
      const message = 'Loading more items';
      onAccessibilityAnnouncement?.(message);
      AccessibilityInfo.announceForAccessibility(message);
    }
  }, [announceLoadingStates, loading, onAccessibilityAnnouncement]);

  // Render skeleton items
  const renderSkeletonItems = () => {
    if (!showSkeleton || !skeletonComponent) return null;
    
    return Array.from({ length: skeletonCount }, (_, index) => (
      <View key={`skeleton-${index}`}>
        {skeletonComponent}
      </View>
    ));
  };

  // Render loading footer
  const renderFooter = () => {
    if (loading) {
      return loadingComponent || (
        <View style={styles.loadingFooter} testID="loading-indicator">
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading more...</Text>
        </View>
      );
    }
    return null;
  };

  // Render empty state
  if (processedData.length === 0 && !loading && !showSkeleton) {
    return (
      <View style={styles.emptyContainer}>
        {emptyComponent || (
          <Text style={styles.emptyText} testID="empty-state">
            No items found
          </Text>
        )}
      </View>
    );
  }

  // Render error state
  if (error) {
    return (
      <View style={styles.errorContainer}>
        {errorComponent || (
          <Text style={styles.errorText} testID="error-state">
            Failed to load items
          </Text>
        )}
      </View>
    );
  }

  // Render skeleton loading
  if (showSkeleton) {
    return (
      <View style={styles.skeletonContainer}>
        {renderSkeletonItems()}
      </View>
    );
  }

  return (
    <FlatList
      ref={flatListRef}
      data={visibleData}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      onEndReached={handleEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      onScroll={handleScroll}
      onViewableItemsChanged={handleViewableItemsChanged}
      ListFooterComponent={renderFooter}
      refreshControl={
        enablePullToRefresh ? (
          <RefreshControl
            refreshing={refreshing || false}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        ) : undefined
      }
      // Performance optimizations
      removeClippedSubviews={enableVirtualization}
      windowSize={windowSize}
      maxToRenderPerBatch={maxToRenderPerBatch}
      updateCellsBatchingPeriod={50}
      initialNumToRender={initialBatchSize}
      getItemLayout={undefined} // Would implement for known item heights
      // Accessibility
      keyboardDismissMode={enableKeyboardNavigation ? 'on-drag' : 'none'}
      // Test ID
      testID={testID || 'mock-flatlist'}
      {...flatListProps}
    />
  );
}

const styles = StyleSheet.create({
  loadingFooter: {
    padding: 16,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: '#666666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#999999',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
  },
  skeletonContainer: {
    flex: 1,
  },
});
