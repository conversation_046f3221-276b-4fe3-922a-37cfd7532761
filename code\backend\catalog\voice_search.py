"""
Voice Search Service for Vierla Beauty Services Marketplace

This module provides speech-to-text functionality and natural language processing
for voice search queries in the service catalog.
"""

import io
import json
import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from django.conf import settings
from django.core.cache import cache
import tempfile
import os

logger = logging.getLogger(__name__)


class SpeechToTextService:
    """
    Speech-to-text service using Google Cloud Speech API
    with fallback to other providers
    """
    
    def __init__(self):
        self.provider = getattr(settings, 'SPEECH_TO_TEXT_PROVIDER', 'google')
        self.cache_timeout = getattr(settings, 'SPEECH_CACHE_TIMEOUT', 3600)  # 1 hour
        
        # Initialize the appropriate client
        if self.provider == 'google':
            self._init_google_client()
        elif self.provider == 'azure':
            self._init_azure_client()
        else:
            self._init_mock_client()
    
    def _init_google_client(self):
        """Initialize Google Cloud Speech client"""
        try:
            from google.cloud import speech
            self.client = speech.SpeechClient()
            self.config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.WEBM_OPUS,
                sample_rate_hertz=16000,
                language_code="en-US",
                enable_automatic_punctuation=True,
                enable_word_confidence=True,
                enable_word_time_offsets=True,
                profanity_filter=True,
                speech_contexts=[
                    speech.SpeechContext(
                        phrases=[
                            "beauty salon", "hair salon", "spa", "massage", "facial",
                            "manicure", "pedicure", "nail salon", "barbershop",
                            "eyebrow threading", "waxing", "makeup", "skincare"
                        ]
                    )
                ]
            )
            logger.info("Google Cloud Speech client initialized")
        except ImportError:
            logger.warning("Google Cloud Speech library not available, using mock client")
            self._init_mock_client()
        except Exception as e:
            logger.error(f"Failed to initialize Google Speech client: {e}")
            self._init_mock_client()
    
    def _init_azure_client(self):
        """Initialize Azure Speech client"""
        try:
            import azure.cognitiveservices.speech as speechsdk
            
            speech_key = getattr(settings, 'AZURE_SPEECH_KEY', None)
            service_region = getattr(settings, 'AZURE_SPEECH_REGION', 'eastus')
            
            if not speech_key:
                raise ValueError("AZURE_SPEECH_KEY not configured")
            
            speech_config = speechsdk.SpeechConfig(
                subscription=speech_key, 
                region=service_region
            )
            speech_config.speech_recognition_language = "en-US"
            speech_config.enable_dictation()
            
            self.speech_config = speech_config
            logger.info("Azure Speech client initialized")
        except ImportError:
            logger.warning("Azure Speech library not available, using mock client")
            self._init_mock_client()
        except Exception as e:
            logger.error(f"Failed to initialize Azure Speech client: {e}")
            self._init_mock_client()
    
    def _init_mock_client(self):
        """Initialize mock client for development/testing"""
        self.client = None
        self.provider = 'mock'
        logger.info("Mock speech client initialized")
    
    def transcribe_audio(self, audio_data: bytes, language_code: str = 'en-US') -> Dict[str, Any]:
        """
        Transcribe audio data to text
        
        Args:
            audio_data: Raw audio bytes
            language_code: Language code for transcription
            
        Returns:
            Dictionary containing transcription results
        """
        # Generate cache key
        import hashlib
        cache_key = f"speech_transcription_{hashlib.md5(audio_data).hexdigest()}"
        
        # Check cache first
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.info("Returning cached transcription result")
            return cached_result
        
        try:
            if self.provider == 'google':
                result = self._transcribe_google(audio_data, language_code)
            elif self.provider == 'azure':
                result = self._transcribe_azure(audio_data, language_code)
            else:
                result = self._transcribe_mock(audio_data, language_code)
            
            # Cache the result
            cache.set(cache_key, result, timeout=self.cache_timeout)
            return result
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'transcript': '',
                'confidence': 0.0
            }
    
    def _transcribe_google(self, audio_data: bytes, language_code: str) -> Dict[str, Any]:
        """Transcribe using Google Cloud Speech API"""
        from google.cloud import speech
        
        # Update config for the specific language
        config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.WEBM_OPUS,
            sample_rate_hertz=16000,
            language_code=language_code,
            enable_automatic_punctuation=True,
            enable_word_confidence=True,
            profanity_filter=True,
            speech_contexts=[
                speech.SpeechContext(
                    phrases=[
                        "beauty salon", "hair salon", "spa", "massage", "facial",
                        "manicure", "pedicure", "nail salon", "barbershop",
                        "eyebrow threading", "waxing", "makeup", "skincare"
                    ]
                )
            ]
        )
        
        audio = speech.RecognitionAudio(content=audio_data)
        response = self.client.recognize(config=config, audio=audio)
        
        if not response.results:
            return {
                'success': True,
                'transcript': '',
                'confidence': 0.0,
                'alternatives': []
            }
        
        # Get the best result
        result = response.results[0]
        alternative = result.alternatives[0]
        
        return {
            'success': True,
            'transcript': alternative.transcript,
            'confidence': alternative.confidence,
            'alternatives': [
                {
                    'transcript': alt.transcript,
                    'confidence': alt.confidence
                }
                for alt in result.alternatives[:3]  # Top 3 alternatives
            ],
            'words': [
                {
                    'word': word.word,
                    'confidence': word.confidence,
                    'start_time': word.start_time.total_seconds(),
                    'end_time': word.end_time.total_seconds()
                }
                for word in alternative.words
            ] if hasattr(alternative, 'words') else []
        }
    
    def _transcribe_azure(self, audio_data: bytes, language_code: str) -> Dict[str, Any]:
        """Transcribe using Azure Speech API"""
        import azure.cognitiveservices.speech as speechsdk
        
        # Create a temporary file for the audio
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            temp_file.write(audio_data)
            temp_file_path = temp_file.name
        
        try:
            # Create audio config from file
            audio_config = speechsdk.audio.AudioConfig(filename=temp_file_path)
            
            # Update language
            speech_config = self.speech_config
            speech_config.speech_recognition_language = language_code
            
            # Create recognizer
            speech_recognizer = speechsdk.SpeechRecognizer(
                speech_config=speech_config, 
                audio_config=audio_config
            )
            
            # Perform recognition
            result = speech_recognizer.recognize_once()
            
            if result.reason == speechsdk.ResultReason.RecognizedSpeech:
                return {
                    'success': True,
                    'transcript': result.text,
                    'confidence': 0.9,  # Azure doesn't provide confidence in basic tier
                    'alternatives': []
                }
            elif result.reason == speechsdk.ResultReason.NoMatch:
                return {
                    'success': True,
                    'transcript': '',
                    'confidence': 0.0,
                    'alternatives': []
                }
            else:
                return {
                    'success': False,
                    'error': f"Recognition failed: {result.reason}",
                    'transcript': '',
                    'confidence': 0.0
                }
        
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    def _transcribe_mock(self, audio_data: bytes, language_code: str) -> Dict[str, Any]:
        """Mock transcription for development/testing"""
        # Simple mock responses based on audio data length
        audio_length = len(audio_data)
        
        if audio_length < 1000:
            transcript = "hair salon"
        elif audio_length < 5000:
            transcript = "find massage therapist near me"
        elif audio_length < 10000:
            transcript = "show me the best rated spas in Toronto"
        else:
            transcript = "book a facial appointment for tomorrow afternoon"
        
        return {
            'success': True,
            'transcript': transcript,
            'confidence': 0.95,
            'alternatives': [
                {'transcript': transcript, 'confidence': 0.95},
                {'transcript': transcript.replace('me', 'us'), 'confidence': 0.85}
            ],
            'provider': 'mock'
        }


class VoiceQueryProcessor:
    """
    Natural language processor for voice search queries
    Converts speech transcriptions into structured search parameters
    """
    
    def __init__(self):
        self.service_keywords = {
            'hair': ['hair', 'haircut', 'hairstyle', 'styling', 'color', 'highlights'],
            'spa': ['spa', 'relaxation', 'wellness', 'retreat'],
            'massage': ['massage', 'therapy', 'therapeutic', 'deep tissue', 'swedish'],
            'facial': ['facial', 'skincare', 'skin care', 'cleansing', 'anti-aging'],
            'nails': ['nails', 'nail', 'manicure', 'pedicure', 'nail art', 'gel nails', 'nail salon'],
            'makeup': ['makeup', 'cosmetics', 'beauty', 'makeover'],
            'waxing': ['waxing', 'hair removal', 'brazilian', 'eyebrow'],
            'barbershop': ['barber', 'barbershop', 'mens haircut', 'beard trim']
        }
        
        self.location_patterns = [
            r'in\s+([A-Za-z\s]+?)(?:\s|$)',
            r'near\s+([A-Za-z\s]+?)(?:\s|$)',
            r'around\s+([A-Za-z\s]+?)(?:\s|$)',
            r'at\s+([A-Za-z\s]+?)(?:\s|$)'
        ]
        
        self.intent_patterns = {
            'compare': [r'compare', r'best', r'top rated', r'highest rated', r'show me best'],
            'book': [r'book', r'schedule', r'appointment', r'reserve'],
            'info': [r'what is', r'tell me about', r'information', r'details'],
            'search': [r'find', r'show', r'search', r'look for', r'get me']
        }
    
    def process_query(self, transcript: str) -> Dict[str, Any]:
        """
        Process voice transcript into structured search parameters
        
        Args:
            transcript: Transcribed speech text
            
        Returns:
            Dictionary with extracted search parameters
        """
        transcript = transcript.lower().strip()
        
        if not transcript:
            return {'intent': 'unknown', 'query': '', 'filters': {}}
        
        # Extract intent
        intent = self._extract_intent(transcript)
        
        # Extract service categories
        categories = self._extract_service_categories(transcript)
        
        # Extract location
        location = self._extract_location(transcript)
        
        # Extract other filters
        filters = self._extract_filters(transcript)
        
        # Generate search query
        search_query = self._generate_search_query(transcript, categories)
        
        return {
            'intent': intent,
            'query': search_query,
            'original_transcript': transcript,
            'categories': categories,
            'location': location,
            'filters': filters,
            'confidence': self._calculate_confidence(transcript, categories, location)
        }
    
    def _extract_intent(self, transcript: str) -> str:
        """Extract user intent from transcript"""
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, transcript, re.IGNORECASE):
                    return intent
        return 'search'  # Default intent
    
    def _extract_service_categories(self, transcript: str) -> List[str]:
        """Extract service categories from transcript"""
        found_categories = []
        
        for category, keywords in self.service_keywords.items():
            for keyword in keywords:
                if keyword in transcript:
                    found_categories.append(category)
                    break
        
        return list(set(found_categories))  # Remove duplicates
    
    def _extract_location(self, transcript: str) -> Optional[str]:
        """Extract location from transcript"""
        for pattern in self.location_patterns:
            match = re.search(pattern, transcript, re.IGNORECASE)
            if match:
                location = match.group(1).strip()
                # Clean up common words
                location = re.sub(r'\b(me|us|here)\b', '', location, flags=re.IGNORECASE).strip()
                if location:
                    return location.title()
        return None
    
    def _extract_filters(self, transcript: str) -> Dict[str, Any]:
        """Extract additional filters from transcript"""
        filters = {}
        
        # Price filters
        if re.search(r'cheap|affordable|budget|low cost', transcript, re.IGNORECASE):
            filters['price_range'] = 'low'
        elif re.search(r'expensive|premium|luxury|high end', transcript, re.IGNORECASE):
            filters['price_range'] = 'high'
        
        # Rating filters
        if re.search(r'best|top rated|highest rated|excellent', transcript, re.IGNORECASE):
            filters['min_rating'] = 4.5
        elif re.search(r'good|well rated', transcript, re.IGNORECASE):
            filters['min_rating'] = 4.0
        
        # Time filters
        if re.search(r'today|now|immediately', transcript, re.IGNORECASE):
            filters['availability'] = 'today'
        elif re.search(r'tomorrow', transcript, re.IGNORECASE):
            filters['availability'] = 'tomorrow'
        elif re.search(r'this week|soon', transcript, re.IGNORECASE):
            filters['availability'] = 'week'
        
        return filters
    
    def _generate_search_query(self, transcript: str, categories: List[str]) -> str:
        """Generate search query from transcript and categories"""
        # Remove common stop words and location phrases
        query = re.sub(r'\b(find|show|me|get|search|for|the|a|an|in|near|around|at)\b', 
                      '', transcript, flags=re.IGNORECASE)
        
        # Clean up extra spaces
        query = ' '.join(query.split())
        
        # If no meaningful query remains, use categories
        if not query or len(query) < 3:
            query = ' '.join(categories) if categories else transcript
        
        return query.strip()
    
    def _calculate_confidence(self, transcript: str, categories: List[str], location: Optional[str]) -> float:
        """Calculate confidence score for the processed query"""
        confidence = 0.5  # Base confidence
        
        # Boost confidence for recognized categories
        if categories:
            confidence += 0.3
        
        # Boost confidence for recognized location
        if location:
            confidence += 0.2
        
        # Boost confidence for clear intent words
        intent_words = ['find', 'show', 'book', 'search', 'get', 'best']
        if any(word in transcript.lower() for word in intent_words):
            confidence += 0.1
        
        return min(confidence, 1.0)


class VoiceSearchService:
    """
    Main voice search service that combines speech-to-text and query processing
    """
    
    def __init__(self):
        self.speech_service = SpeechToTextService()
        self.query_processor = VoiceQueryProcessor()
    
    def process_voice_search(self, audio_data: bytes, language_code: str = 'en-US') -> Dict[str, Any]:
        """
        Complete voice search processing pipeline
        
        Args:
            audio_data: Raw audio bytes
            language_code: Language code for transcription
            
        Returns:
            Dictionary with transcription and processed search parameters
        """
        # Step 1: Transcribe audio to text
        transcription_result = self.speech_service.transcribe_audio(audio_data, language_code)
        
        if not transcription_result['success']:
            return {
                'success': False,
                'error': transcription_result.get('error', 'Transcription failed'),
                'stage': 'transcription'
            }
        
        transcript = transcription_result['transcript']
        
        # Step 2: Process the transcript into search parameters
        query_result = self.query_processor.process_query(transcript)
        
        # Step 3: Combine results
        return {
            'success': True,
            'transcription': transcription_result,
            'query': query_result,
            'search_ready': bool(query_result['query'] or query_result['categories'])
        }
