"""
Django management command to rebuild the search index
"""

from django.core.management.base import BaseCommand
from catalog.search_algorithms import SearchIndexManager


class Command(BaseCommand):
    help = 'Rebuild the search index for all services'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force rebuild even if index exists',
        )

    def handle(self, *args, **options):
        self.stdout.write('Starting search index rebuild...')

        index_manager = SearchIndexManager()

        if options['force'] or not index_manager.index_exists():
            result = index_manager.rebuild_index()

            if result['success']:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully rebuilt search index. '
                        f'Indexed {result["indexed_count"]} services.'
                    )
                )
            else:
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to rebuild search index: {result.get("error", "Unknown error")}'
                    )
                )
        else:
            self.stdout.write(
                self.style.WARNING(
                    'Search index already exists. Use --force to rebuild anyway.'
                )
            )