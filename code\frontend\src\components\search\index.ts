/**
 * Search Components Index
 * Centralized exports for all search-related components and utilities
 */

export {
  EnhancedSearchSystem,
  SearchProvider,
  SearchFilters,
  SearchSuggestions,
  useSearchAnalytics,
} from './EnhancedSearchSystem';

export type {
  SearchFilters as SearchFiltersType,
  SearchSuggestion,
  SearchResult,
  SearchAnalytics,
  EnhancedSearchSystemProps,
} from './EnhancedSearchSystem';

// Re-export search API service types
export type {
  SearchParams,
  SearchResponse,
  VoiceSearchRequest,
  VoiceSearchResponse,
  SearchAnalyticsData,
  RecommendationParams,
} from '../../services/searchApiService';

export { searchApiService } from '../../services/searchApiService';
